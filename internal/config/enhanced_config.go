package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

// EnhancedDatabaseConfig 增强的数据库配置
type EnhancedDatabaseConfig struct {
	// 基础配置
	Type                string        `json:"type"`                  // postgresql, sqlite
	DSN                 string        `json:"dsn"`                   // 连接字符串
	MaxOpenConns        int           `json:"max_open_conns"`        // 最大连接数
	MaxIdleConns        int           `json:"max_idle_conns"`        // 最大空闲连接数
	ConnMaxLifetime     time.Duration `json:"conn_max_lifetime"`     // 连接最大生命周期
	
	// 兼容性配置
	EnablePartitioning      bool     `json:"enable_partitioning"`       // 是否启用分区
	EnableMaterializedViews bool     `json:"enable_materialized_views"` // 是否启用物化视图
	JSONIndexPaths          []string `json:"json_index_paths"`           // JSON索引路径
	
	// 性能配置
	EnableQueryLogging      bool          `json:"enable_query_logging"`      // 是否启用查询日志
	SlowQueryThreshold      time.Duration `json:"slow_query_threshold"`      // 慢查询阈值
	EnablePerformanceMonitor bool         `json:"enable_performance_monitor"` // 是否启用性能监控
	
	// 安全配置
	EnableSSL               bool   `json:"enable_ssl"`                // 是否启用SSL
	SSLMode                 string `json:"ssl_mode"`                  // SSL模式
	EnableConnectionPooling bool   `json:"enable_connection_pooling"` // 是否启用连接池
}

// EnhancedConfig 增强的应用配置
type EnhancedConfig struct {
	Environment string                 `json:"environment"` // development, production, testing
	Database    EnhancedDatabaseConfig `json:"database"`
	Cache       CacheConfig            `json:"cache"`
	AI          AIConfig               `json:"ai"`
	Server      ServerConfig           `json:"server"`
	Logging     LoggingConfig          `json:"logging"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string        `json:"host"`
	Port         int           `json:"port"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Type     string `json:"type"`     // redis, memory
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Password string `json:"password"`
	DB       int    `json:"db"`
}

// AIConfig AI配置
type AIConfig struct {
	Provider    string `json:"provider"`     // windmill, openai
	APIKey      string `json:"api_key"`
	BaseURL     string `json:"base_url"`
	DefaultModel string `json:"default_model"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level      string `json:"level"`       // debug, info, warn, error
	Format     string `json:"format"`      // json, text
	Output     string `json:"output"`      // stdout, file
	FilePath   string `json:"file_path"`
	MaxSize    int    `json:"max_size"`    // MB
	MaxBackups int    `json:"max_backups"`
	MaxAge     int    `json:"max_age"`     // days
}

// LoadEnhancedConfig 加载增强配置
func LoadEnhancedConfig() (*EnhancedConfig, error) {
	config := &EnhancedConfig{}
	
	// 从环境变量加载配置
	config.Environment = getEnv("ENVIRONMENT", "development")
	
	// 数据库配置
	config.Database = EnhancedDatabaseConfig{
		Type:                    getEnv("DB_TYPE", detectDatabaseType()),
		DSN:                     getEnv("DB_DSN", getDefaultDSN()),
		MaxOpenConns:            getEnvInt("DB_MAX_OPEN_CONNS", getDefaultMaxOpenConns(config.Environment)),
		MaxIdleConns:            getEnvInt("DB_MAX_IDLE_CONNS", getDefaultMaxIdleConns(config.Environment)),
		ConnMaxLifetime:         getEnvDuration("DB_CONN_MAX_LIFETIME", time.Hour),
		EnablePartitioning:      getEnvBool("DB_ENABLE_PARTITIONING", config.Environment == "production"),
		EnableMaterializedViews: getEnvBool("DB_ENABLE_MATERIALIZED_VIEWS", config.Environment == "production"),
		JSONIndexPaths:          getEnvStringSlice("DB_JSON_INDEX_PATHS", getDefaultJSONIndexPaths()),
		EnableQueryLogging:      getEnvBool("DB_ENABLE_QUERY_LOGGING", config.Environment == "development"),
		SlowQueryThreshold:      getEnvDuration("DB_SLOW_QUERY_THRESHOLD", 200*time.Millisecond),
		EnablePerformanceMonitor: getEnvBool("DB_ENABLE_PERFORMANCE_MONITOR", true),
		EnableSSL:               getEnvBool("DB_ENABLE_SSL", config.Environment == "production"),
		SSLMode:                 getEnv("DB_SSL_MODE", getDefaultSSLMode(config.Environment)),
		EnableConnectionPooling: getEnvBool("DB_ENABLE_CONNECTION_POOLING", true),
	}
	
	// 服务器配置
	config.Server = ServerConfig{
		Host:         getEnv("SERVER_HOST", "0.0.0.0"),
		Port:         getEnvInt("SERVER_PORT", 8080),
		ReadTimeout:  getEnvDuration("SERVER_READ_TIMEOUT", 30*time.Second),
		WriteTimeout: getEnvDuration("SERVER_WRITE_TIMEOUT", 30*time.Second),
		IdleTimeout:  getEnvDuration("SERVER_IDLE_TIMEOUT", 120*time.Second),
	}
	
	// 缓存配置
	config.Cache = CacheConfig{
		Type:     getEnv("CACHE_TYPE", "redis"),
		Host:     getEnv("CACHE_HOST", "localhost"),
		Port:     getEnvInt("CACHE_PORT", 6379),
		Password: getEnv("CACHE_PASSWORD", ""),
		DB:       getEnvInt("CACHE_DB", 0),
	}
	
	// AI配置
	config.AI = AIConfig{
		Provider:     getEnv("AI_PROVIDER", "windmill"),
		APIKey:       getEnv("AI_API_KEY", ""),
		BaseURL:      getEnv("AI_BASE_URL", ""),
		DefaultModel: getEnv("AI_DEFAULT_MODEL", "gpt-3.5-turbo"),
	}
	
	// 日志配置
	config.Logging = LoggingConfig{
		Level:      getEnv("LOG_LEVEL", getDefaultLogLevel(config.Environment)),
		Format:     getEnv("LOG_FORMAT", "json"),
		Output:     getEnv("LOG_OUTPUT", "stdout"),
		FilePath:   getEnv("LOG_FILE_PATH", "./logs/app.log"),
		MaxSize:    getEnvInt("LOG_MAX_SIZE", 100),
		MaxBackups: getEnvInt("LOG_MAX_BACKUPS", 3),
		MaxAge:     getEnvInt("LOG_MAX_AGE", 28),
	}
	
	return config, nil
}

// 辅助函数

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getEnvStringSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}

// 默认值函数

func detectDatabaseType() string {
	// 根据环境变量或文件存在性检测数据库类型
	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		if strings.HasSuffix(dbName, ".db") {
			return "sqlite"
		}
	}
	
	// 检查是否存在SQLite文件
	if _, err := os.Stat("game.db"); err == nil {
		return "sqlite"
	}
	
	// 默认使用PostgreSQL
	return "postgresql"
}

func getDefaultDSN() string {
	dbType := detectDatabaseType()
	if dbType == "sqlite" {
		return "game.db"
	}
	
	// PostgreSQL默认DSN
	host := getEnv("DB_HOST", "localhost")
	port := getEnvInt("DB_PORT", 5432)
	user := getEnv("DB_USER", "postgres")
	password := getEnv("DB_PASSWORD", "")
	dbname := getEnv("DB_NAME", "ai_text_game")
	sslmode := getEnv("DB_SSL_MODE", "disable")
	
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		host, port, user, password, dbname, sslmode)
}

func getDefaultMaxOpenConns(environment string) int {
	switch environment {
	case "production":
		return 100
	case "testing":
		return 10
	default:
		return 25
	}
}

func getDefaultMaxIdleConns(environment string) int {
	switch environment {
	case "production":
		return 20
	case "testing":
		return 2
	default:
		return 5
	}
}

func getDefaultJSONIndexPaths() []string {
	return []string{
		"display_name", "locale", "theme", "genre", "complexity",
		"ui.theme", "ui.language", "game.ai_speed", "game.language",
		"theme.genre", "theme.mood", "rules.content_rating",
		"environment.weather", "environment.time", "accessibility.is_accessible",
	}
}

func getDefaultSSLMode(environment string) string {
	if environment == "production" {
		return "require"
	}
	return "disable"
}

func getDefaultLogLevel(environment string) string {
	switch environment {
	case "production":
		return "info"
	case "development":
		return "debug"
	case "testing":
		return "warn"
	default:
		return "info"
	}
}

// Validate 验证配置
func (c *EnhancedConfig) Validate() error {
	// 验证数据库配置
	if c.Database.Type == "" {
		return fmt.Errorf("数据库类型不能为空")
	}
	
	if c.Database.Type != "postgresql" && c.Database.Type != "sqlite" {
		return fmt.Errorf("不支持的数据库类型: %s", c.Database.Type)
	}
	
	if c.Database.DSN == "" {
		return fmt.Errorf("数据库连接字符串不能为空")
	}
	
	// 验证服务器配置
	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("无效的服务器端口: %d", c.Server.Port)
	}
	
	// 验证AI配置
	if c.AI.Provider == "" {
		return fmt.Errorf("AI提供商不能为空")
	}
	
	return nil
}

// GetDatabaseDSN 获取数据库连接字符串
func (c *EnhancedConfig) GetDatabaseDSN() string {
	return c.Database.DSN
}

// IsProduction 是否为生产环境
func (c *EnhancedConfig) IsProduction() bool {
	return c.Environment == "production"
}

// IsDevelopment 是否为开发环境
func (c *EnhancedConfig) IsDevelopment() bool {
	return c.Environment == "development"
}

// IsTesting 是否为测试环境
func (c *EnhancedConfig) IsTesting() bool {
	return c.Environment == "testing"
}
