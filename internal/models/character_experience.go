package models

import (
	"time"
	"gorm.io/gorm"
)

// CharacterExperience 角色阅历模型 - 兼容SQLite和PostgreSQL
// 存储角色的技能、知识和经验，支持熟练度系统和传承机制
type CharacterExperience struct {
	// 基础字段（使用string类型兼容SQLite）
	ID          string `gorm:"primaryKey;type:text" json:"id"`
	CharacterID string `gorm:"type:text;not null;index" json:"character_id"`

	// 阅历分类（高频使用）
	Tags        StringArray `gorm:"type:text;not null;default:'[]'" json:"tags"`        // 阅历标签（替代固定分类）
	Description string      `gorm:"type:text;not null" json:"description"`             // 阅历描述

	// 熟练度系统（高频使用）
	Proficiency JSON `gorm:"type:text;not null;default:'{}'" json:"proficiency"` // 熟练度信息

	// 传承和影响（中频使用）
	LearningInfo JSON `gorm:"type:text;default:'{}'" json:"learning_info"` // 学习和传承信息
	SocialImpact JSON `gorm:"type:text;default:'{}'" json:"social_impact"` // 社交影响

	// 应用效果（中频使用）
	Effects JSON `gorm:"type:text;default:'{}'" json:"effects"` // 阅历效果

	// 时间信息
	AcquiredAt time.Time `gorm:"default:(datetime('now'))" json:"acquired_at"` // 获得时间
	LastUsed   time.Time `gorm:"default:(datetime('now'));index" json:"last_used"` // 最后使用时间
	UpdatedAt  time.Time `gorm:"default:(datetime('now'))" json:"updated_at"`

	// 关联关系
	Character Character `gorm:"foreignKey:CharacterID;constraint:OnDelete:CASCADE" json:"character,omitempty"`
}

// TableName 指定表名
func (CharacterExperience) TableName() string {
	return "character_experiences"
}

// BeforeCreate GORM钩子：创建前设置默认值
func (ce *CharacterExperience) BeforeCreate(tx *gorm.DB) error {
	if ce.ID == "" {
		ce.ID = generateExperienceUUID()
	}

	// 设置默认标签
	if len(ce.Tags) == 0 {
		ce.Tags = StringArray{"general"}
	}

	// 设置默认熟练度
	if len(ce.Proficiency) == 0 {
		ce.Proficiency = JSON{
			"level":             1,
			"experience_points": 0,
			"specializations":   []interface{}{},
			"mastery_level":     "novice",
		}
	}

	// 设置默认学习信息
	if len(ce.LearningInfo) == 0 {
		ce.LearningInfo = JSON{
			"source":          "self_taught",
			"teachers":        []interface{}{},
			"learning_method": "practice",
		}
	}

	// 设置默认社交影响
	if len(ce.SocialImpact) == 0 {
		ce.SocialImpact = JSON{
			"reputation":        0,
			"recognition_level": "unknown",
			"influence_network": []interface{}{},
		}
	}

	// 设置默认效果
	if len(ce.Effects) == 0 {
		ce.Effects = JSON{
			"stat_modifiers":   map[string]interface{}{},
			"skill_bonuses":    map[string]interface{}{},
			"special_abilities": []interface{}{},
		}
	}

	return nil
}

// BeforeUpdate GORM钩子：更新前更新使用时间
func (ce *CharacterExperience) BeforeUpdate(tx *gorm.DB) error {
	ce.LastUsed = time.Now()
	return nil
}

// GetTags 获取阅历标签列表
func (ce *CharacterExperience) GetTags() []string {
	return []string(ce.Tags)
}

// HasTag 检查是否包含指定标签
func (ce *CharacterExperience) HasTag(tag string) bool {
	for _, t := range ce.Tags {
		if t == tag {
			return true
		}
	}
	return false
}

// AddTag 添加阅历标签
func (ce *CharacterExperience) AddTag(tag string) {
	// 检查是否已存在
	for _, t := range ce.Tags {
		if t == tag {
			return // 已存在，无需添加
		}
	}

	// 添加新标签
	ce.Tags = append(ce.Tags, tag)
}

// GetProficiencyLevel 获取熟练度等级
func (ce *CharacterExperience) GetProficiencyLevel() int {
	proficiency := map[string]interface{}(ce.Proficiency)

	if level, ok := proficiency["level"].(float64); ok {
		return int(level)
	}

	return 1
}

// GetExperiencePoints 获取经验值
func (ce *CharacterExperience) GetExperiencePoints() int {
	proficiency := map[string]interface{}(ce.Proficiency)

	if exp, ok := proficiency["experience_points"].(float64); ok {
		return int(exp)
	}

	return 0
}

// AddExperience 增加经验值
func (ce *CharacterExperience) AddExperience(points int) {
	if ce.Proficiency == nil {
		ce.Proficiency = make(JSON)
	}

	proficiency := map[string]interface{}(ce.Proficiency)

	// 获取当前经验值
	currentExp := 0
	if exp, ok := proficiency["experience_points"].(float64); ok {
		currentExp = int(exp)
	}

	// 增加经验值
	newExp := currentExp + points
	proficiency["experience_points"] = newExp

	// 检查是否升级
	currentLevel := 1
	if level, ok := proficiency["level"].(float64); ok {
		currentLevel = int(level)
	}

	// 简单的升级公式：每100经验值升1级
	newLevel := newExp/100 + 1
	if newLevel > currentLevel {
		proficiency["level"] = newLevel

		// 更新熟练度等级描述
		masteryLevel := "novice"
		if newLevel >= 10 {
			masteryLevel = "expert"
		} else if newLevel >= 5 {
			masteryLevel = "advanced"
		} else if newLevel >= 3 {
			masteryLevel = "intermediate"
		}
		proficiency["mastery_level"] = masteryLevel
	}

	// 更新熟练度数据
	ce.Proficiency = JSON(proficiency)
	ce.LastUsed = time.Now()
}

// generateExperienceUUID 生成阅历UUID字符串
func generateExperienceUUID() string {
	return time.Now().Format("20060102150405") + "-experience"
}