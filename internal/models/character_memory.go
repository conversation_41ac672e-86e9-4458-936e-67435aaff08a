package models

import (
	"time"
	"gorm.io/gorm"
)

// CharacterMemory 角色记忆模型 - 兼容SQLite和PostgreSQL
// 存储角色的记忆信息，支持记忆强度和衰减机制
type CharacterMemory struct {
	// 基础字段（使用string类型兼容SQLite）
	ID          string `gorm:"primaryKey;type:text" json:"id"`
	CharacterID string `gorm:"type:text;not null;index" json:"character_id"`

	// 记忆内容（高频使用）
	Content    string `gorm:"type:text;not null" json:"content"`                                    // 记忆内容描述
	MemoryType string `gorm:"type:varchar(50);not null;index:idx_memory_type" json:"memory_type"` // event, person, location, item, knowledge, emotion

	// 记忆强度系统（中频使用）
	ImportanceScore float64 `gorm:"type:real;not null;default:0.5;check:importance_score >= 0 AND importance_score <= 1;index:idx_importance" json:"importance_score"` // 重要性评分 (0-1)
	EmotionalImpact float64 `gorm:"type:real;not null;default:0.0;check:emotional_impact >= -1 AND emotional_impact <= 1" json:"emotional_impact"`                    // 情感影响 (-1到1)
	CurrentStrength float64 `gorm:"type:real;not null;default:1.0;check:current_strength >= 0 AND current_strength <= 1;index:idx_strength" json:"current_strength"` // 当前记忆强度 (0-1)

	// 记忆衰减（中频使用）
	DecayRate    float64   `gorm:"type:real;not null;default:0.05;check:decay_rate >= 0 AND decay_rate <= 1" json:"decay_rate"` // 衰减率 (0-1)
	LastAccessed time.Time `gorm:"default:(datetime('now'))" json:"last_accessed"`                                               // 最后访问时间

	// 关联信息（低频使用）
	AssociatedEntities StringArray `gorm:"type:text;default:'[]'" json:"associated_entities"` // 关联的实体ID
	Tags               StringArray `gorm:"type:text;default:'[]'" json:"tags"`                // 记忆标签
	Context            JSON        `gorm:"type:text;default:'{}'" json:"context"`             // 记忆上下文信息

	// 时间戳
	CreatedAt time.Time `gorm:"default:(datetime('now'))" json:"created_at"`
	UpdatedAt time.Time `gorm:"default:(datetime('now'))" json:"updated_at"`

	// 关联关系
	Character Character `gorm:"foreignKey:CharacterID;constraint:OnDelete:CASCADE" json:"character,omitempty"`
}

// TableName 指定表名
func (CharacterMemory) TableName() string {
	return "character_memories"
}

// BeforeCreate GORM钩子：创建前设置默认值
func (cm *CharacterMemory) BeforeCreate(tx *gorm.DB) error {
	if cm.ID == "" {
		cm.ID = generateMemoryUUID()
	}

	// 设置默认标签
	if len(cm.Tags) == 0 {
		cm.Tags = StringArray{}
	}

	// 设置默认关联实体
	if len(cm.AssociatedEntities) == 0 {
		cm.AssociatedEntities = StringArray{}
	}

	// 设置默认上下文
	if len(cm.Context) == 0 {
		cm.Context = JSON{}
	}

	return nil
}

// BeforeUpdate GORM钩子：更新前更新访问时间
func (cm *CharacterMemory) BeforeUpdate(tx *gorm.DB) error {
	cm.LastAccessed = time.Now()
	return nil
}

// ApplyDecay 应用记忆衰减
func (cm *CharacterMemory) ApplyDecay(timePassed time.Duration) {
	// 计算基础衰减量（基于时间和衰减率）
	decayAmount := cm.DecayRate * timePassed.Hours() / 24.0

	// 重要记忆衰减更慢
	importanceResistance := cm.ImportanceScore * 0.5 // 重要性提供衰减抗性
	actualDecay := decayAmount * (1.0 - importanceResistance)

	// 应用衰减
	cm.CurrentStrength -= actualDecay

	// 确保强度不低于0
	if cm.CurrentStrength < 0 {
		cm.CurrentStrength = 0
	}
}

// IsAccessible 检查记忆是否可访问（强度足够）
func (cm *CharacterMemory) IsAccessible() bool {
	return cm.CurrentStrength > 0.1 // 强度低于0.1的记忆难以回忆
}

// GetRelevanceScore 计算记忆的相关性评分（用于检索排序）
func (cm *CharacterMemory) GetRelevanceScore(queryTags []string) float64 {
	// 基础评分 = 重要性 * 当前强度
	baseScore := cm.ImportanceScore * cm.CurrentStrength

	// 标签匹配加成
	var tagBonus float64 = 0
	if len(queryTags) > 0 {
		memoryTags := []string(cm.Tags)
		matchCount := 0
		for _, queryTag := range queryTags {
			for _, memoryTag := range memoryTags {
				if queryTag == memoryTag {
					matchCount++
					break
				}
			}
		}
		tagBonus = float64(matchCount) / float64(len(queryTags)) * 0.3
	}

	// 时间衰减（最近的记忆更相关）
	timeSinceCreated := time.Since(cm.CreatedAt)
	timeBonus := 1.0 / (1.0 + timeSinceCreated.Hours()/24.0/30.0) * 0.2 // 30天半衰期

	return baseScore + tagBonus + timeBonus
}

// Reinforce 强化记忆（被访问时调用）
func (cm *CharacterMemory) Reinforce(reinforcementStrength float64) {
	// 强化记忆强度，但不能超过1.0
	cm.CurrentStrength += reinforcementStrength * (1.0 - cm.CurrentStrength)
	if cm.CurrentStrength > 1.0 {
		cm.CurrentStrength = 1.0
	}

	// 更新访问时间
	cm.LastAccessed = time.Now()
}

// GetAssociatedEntityIDs 获取关联的实体ID列表
func (cm *CharacterMemory) GetAssociatedEntityIDs() []string {
	return []string(cm.AssociatedEntities)
}

// AddAssociatedEntity 添加关联实体
func (cm *CharacterMemory) AddAssociatedEntity(entityID string) {
	// 检查是否已存在
	for _, id := range cm.AssociatedEntities {
		if id == entityID {
			return // 已存在，无需添加
		}
	}

	// 添加新的实体ID
	cm.AssociatedEntities = append(cm.AssociatedEntities, entityID)
}

// GetTags 获取记忆标签列表
func (cm *CharacterMemory) GetTags() []string {
	return []string(cm.Tags)
}

// AddTag 添加记忆标签
func (cm *CharacterMemory) AddTag(tag string) {
	// 检查是否已存在
	for _, t := range cm.Tags {
		if t == tag {
			return // 已存在，无需添加
		}
	}

	// 添加新标签
	cm.Tags = append(cm.Tags, tag)
}

// generateMemoryUUID 生成记忆UUID字符串
func generateMemoryUUID() string {
	return time.Now().Format("20060102150405") + "-memory"
}