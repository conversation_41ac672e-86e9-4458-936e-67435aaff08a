package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func TestCharacterMemory_BeforeCreate(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	err = db.AutoMigrate(&CharacterMemory{})
	require.NoError(t, err)

	memory := &CharacterMemory{
		CharacterID: "char-123",
		Content:     "记住了一个重要的事件",
		MemoryType:  "event",
	}

	err = db.Create(memory).Error
	require.NoError(t, err)

	// 检查ID是否自动生成
	assert.NotEmpty(t, memory.ID)

	// 检查默认值是否设置
	assert.Equal(t, 0.5, memory.ImportanceScore)
	assert.Equal(t, 0.0, memory.EmotionalImpact)
	assert.Equal(t, 1.0, memory.CurrentStrength)
	assert.Equal(t, 0.05, memory.DecayRate)
	assert.NotNil(t, memory.Tags)
	assert.NotNil(t, memory.AssociatedEntities)
	assert.NotNil(t, memory.Context)
}

func TestCharacterMemory_ApplyDecay(t *testing.T) {
	memory := &CharacterMemory{
		ImportanceScore: 0.8,
		CurrentStrength: 1.0,
		DecayRate:       0.1,
	}

	// 应用1天的衰减
	memory.ApplyDecay(24 * time.Hour)

	// 强度应该有所下降，但由于重要性高，衰减较慢
	assert.Less(t, memory.CurrentStrength, 1.0)
	assert.Greater(t, memory.CurrentStrength, 0.5) // 重要记忆衰减较慢

	// 应用更长时间的衰减
	memory.ApplyDecay(7 * 24 * time.Hour) // 7天

	// 强度应该进一步下降
	assert.Less(t, memory.CurrentStrength, 0.8)
}

func TestCharacterMemory_IsAccessible(t *testing.T) {
	memory := &CharacterMemory{
		CurrentStrength: 0.5,
	}

	// 强度为0.5的记忆应该是可访问的
	assert.True(t, memory.IsAccessible())

	// 强度低于0.1的记忆应该不可访问
	memory.CurrentStrength = 0.05
	assert.False(t, memory.IsAccessible())
}

func TestCharacterMemory_GetRelevanceScore(t *testing.T) {
	memory := &CharacterMemory{
		ImportanceScore: 0.8,
		CurrentStrength: 0.9,
		Tags:           StringArray{"combat", "victory"},
		CreatedAt:      time.Now().Add(-1 * time.Hour), // 1小时前创建
	}

	// 测试无标签查询
	score := memory.GetRelevanceScore([]string{})
	expectedBase := 0.8 * 0.9 // importance * strength
	assert.InDelta(t, expectedBase, score, 0.3) // 增加容差，因为有时间加成

	// 测试匹配标签查询
	scoreWithTags := memory.GetRelevanceScore([]string{"combat", "magic"})
	assert.Greater(t, scoreWithTags, score) // 有标签匹配应该得分更高

	// 测试完全匹配标签查询
	scoreFullMatch := memory.GetRelevanceScore([]string{"combat", "victory"})
	assert.Greater(t, scoreFullMatch, scoreWithTags) // 完全匹配应该得分最高
}

func TestCharacterMemory_Reinforce(t *testing.T) {
	memory := &CharacterMemory{
		CurrentStrength: 0.6,
		LastAccessed:    time.Now().Add(-1 * time.Hour),
	}

	oldAccessTime := memory.LastAccessed

	// 强化记忆
	memory.Reinforce(0.3)

	// 强度应该增加
	assert.Greater(t, memory.CurrentStrength, 0.6)
	assert.LessOrEqual(t, memory.CurrentStrength, 1.0) // 不应超过1.0

	// 访问时间应该更新
	assert.True(t, memory.LastAccessed.After(oldAccessTime))
}

func TestCharacterMemory_TagOperations(t *testing.T) {
	memory := &CharacterMemory{
		Tags: StringArray{"combat", "victory"},
	}

	// 测试获取标签
	tags := memory.GetTags()
	assert.Contains(t, tags, "combat")
	assert.Contains(t, tags, "victory")
	assert.Len(t, tags, 2)

	// 测试添加新标签
	memory.AddTag("important")
	tags = memory.GetTags()
	assert.Contains(t, tags, "important")
	assert.Len(t, tags, 3)

	// 测试添加重复标签
	memory.AddTag("combat") // 已存在
	tags = memory.GetTags()
	assert.Len(t, tags, 3) // 长度不应该变化
}

func TestCharacterMemory_EntityOperations(t *testing.T) {
	memory := &CharacterMemory{
		AssociatedEntities: StringArray{"entity-1", "entity-2"},
	}

	// 测试获取关联实体
	entities := memory.GetAssociatedEntityIDs()
	assert.Contains(t, entities, "entity-1")
	assert.Contains(t, entities, "entity-2")
	assert.Len(t, entities, 2)

	// 测试添加新实体
	memory.AddAssociatedEntity("entity-3")
	entities = memory.GetAssociatedEntityIDs()
	assert.Contains(t, entities, "entity-3")
	assert.Len(t, entities, 3)

	// 测试添加重复实体
	memory.AddAssociatedEntity("entity-1") // 已存在
	entities = memory.GetAssociatedEntityIDs()
	assert.Len(t, entities, 3) // 长度不应该变化
}