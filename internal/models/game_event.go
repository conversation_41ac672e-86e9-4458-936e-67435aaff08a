package models

import (
	"time"
	"gorm.io/gorm"
)

// GameEvent 游戏事件模型 - 兼容SQLite和PostgreSQL
// 记录游戏中发生的所有事件，替代原来的events表
type GameEvent struct {
	// 基础字段（使用string类型兼容SQLite）
	ID      string `gorm:"primaryKey;type:text" json:"id"`
	WorldID string `gorm:"type:text;not null;index" json:"world_id"`

	// 事件分类（高频使用）
	EventType string      `gorm:"type:varchar(50);not null;index:idx_event_type" json:"event_type"` // action, evolution, system, heartbeat
	Tags      StringArray `gorm:"type:text;not null;default:'[]'" json:"tags"`                      // 事件标签

	// 参与者信息（高频使用）
	PrimaryActorID *string `gorm:"type:text;index:idx_primary_actor" json:"primary_actor_id,omitempty"` // 主要触发者（软引用）
	Participants   JSON    `gorm:"type:text;not null;default:'[]'" json:"participants"`               // 所有参与者
	SceneID        *string `gorm:"type:text;index:idx_scene" json:"scene_id,omitempty"`               // 发生场景（软引用）

	// 事件内容（高频使用）
	NarrativeText string `gorm:"type:text" json:"narrative_text"`                    // AI生成的叙事文本
	EventData     JSON   `gorm:"type:text;not null;default:'{}'" json:"event_data"` // 事件详细数据

	// 处理结果（高频使用）
	ProcessingResult JSON `gorm:"type:text;default:'{}'" json:"processing_result"` // AI处理结果

	// 时间信息（中频使用）
	GameTime             int64 `gorm:"not null;index:idx_game_time" json:"game_time"`                    // 游戏内时间
	ProcessingDurationMs *int  `gorm:"type:integer" json:"processing_duration_ms,omitempty"`             // 处理耗时

	// 时间戳
	CreatedAt time.Time `gorm:"default:(datetime('now'));index:idx_created_at" json:"created_at"`

	// 关联关系
	World World `gorm:"foreignKey:WorldID;constraint:OnDelete:CASCADE" json:"world,omitempty"`
}

// TableName 指定表名
func (GameEvent) TableName() string {
	return "game_events"
}

// BeforeCreate GORM钩子：创建前设置默认值
func (ge *GameEvent) BeforeCreate(tx *gorm.DB) error {
	if ge.ID == "" {
		ge.ID = generateGameEventUUID()
	}

	// 设置默认标签
	if len(ge.Tags) == 0 {
		ge.Tags = StringArray{}
	}

	// 设置默认参与者
	if len(ge.Participants) == 0 {
		ge.Participants = JSON{}
	}

	// 设置默认事件数据
	if len(ge.EventData) == 0 {
		ge.EventData = JSON{}
	}

	// 设置默认处理结果
	if len(ge.ProcessingResult) == 0 {
		ge.ProcessingResult = JSON{}
	}

	// 如果没有设置游戏时间，使用当前时间戳
	if ge.GameTime == 0 {
		ge.GameTime = time.Now().UnixMilli()
	}

	return nil
}

// GetTags 获取事件标签列表
func (ge *GameEvent) GetTags() []string {
	return []string(ge.Tags)
}

// AddTag 添加事件标签
func (ge *GameEvent) AddTag(tag string) {
	// 检查是否已存在
	for _, t := range ge.Tags {
		if t == tag {
			return // 已存在，无需添加
		}
	}

	// 添加新标签
	ge.Tags = append(ge.Tags, tag)
}

// GetParticipants 获取参与者列表
func (ge *GameEvent) GetParticipants() []map[string]interface{} {
	participantsData := map[string]interface{}(ge.Participants)
	if participants, ok := participantsData["participants"].([]interface{}); ok {
		result := make([]map[string]interface{}, 0)
		for _, p := range participants {
			if participant, ok := p.(map[string]interface{}); ok {
				result = append(result, participant)
			}
		}
		return result
	}
	return []map[string]interface{}{}
}

// AddParticipant 添加参与者
func (ge *GameEvent) AddParticipant(participantID string, participantType string, role string) {
	participants := ge.GetParticipants()

	// 创建新的参与者记录
	newParticipant := map[string]interface{}{
		"id":   participantID,
		"type": participantType, // character, entity, system
		"role": role,            // actor, target, observer, affected
	}

	// 检查是否已存在
	for _, p := range participants {
		if p["id"] == participantID && p["role"] == role {
			return // 已存在相同角色的参与者
		}
	}

	// 添加新参与者
	participants = append(participants, newParticipant)

	// 更新参与者数据
	if ge.Participants == nil {
		ge.Participants = make(JSON)
	}

	participantsData := map[string]interface{}(ge.Participants)
	participantsInterface := make([]interface{}, len(participants))
	for i, p := range participants {
		participantsInterface[i] = p
	}
	participantsData["participants"] = participantsInterface
	ge.Participants = JSON(participantsData)
}

// GetEventDataField 获取事件数据中的特定字段
func (ge *GameEvent) GetEventDataField(field string) interface{} {
	eventData := map[string]interface{}(ge.EventData)
	return eventData[field]
}

// SetEventDataField 设置事件数据中的特定字段
func (ge *GameEvent) SetEventDataField(field string, value interface{}) {
	if ge.EventData == nil {
		ge.EventData = make(JSON)
	}

	eventData := map[string]interface{}(ge.EventData)
	eventData[field] = value
	ge.EventData = JSON(eventData)
}

// generateGameEventUUID 生成游戏事件UUID字符串
func generateGameEventUUID() string {
	return time.Now().Format("20060102150405") + "-event"
}