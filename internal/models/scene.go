package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Scene 场景模型 - 兼容SQLite和PostgreSQL
type Scene struct {
	// 使用string类型存储UUID，兼容SQLite
	ID              string         `json:"id" gorm:"primaryKey;type:text"`
	WorldID         string         `json:"world_id" gorm:"type:text;not null;index"`
	Name            string         `json:"name" gorm:"not null;size:200"`
	Description     *string        `json:"description"`
	SceneType       string         `json:"scene_type" gorm:"default:'normal';index"` // normal, special, hidden（保留向后兼容性）
	// 使用text类型存储JSON，兼容SQLite
	Properties      JSON           `json:"properties" gorm:"type:text;default:'{}'"`
	EntitiesPresent StringArray    `json:"entities_present" gorm:"type:text;default:'[]'"` // 当前场景中的实体ID列表（保留向后兼容性）
	ConnectedScenes JSON           `json:"connected_scenes" gorm:"type:text;default:'{}'"` // 连接的场景（保留向后兼容性）
	Environment     JSON           `json:"environment" gorm:"type:text;default:'{}'"`      // 环境信息

	// 新增字段（v4.0架构）
	Tags        StringArray `json:"tags" gorm:"type:text;default:'[]'"`        // 场景标签（替代scene_type）
	AccessRules JSON        `json:"access_rules" gorm:"type:text;default:'{}'"` // 访问规则
	Connections JSON        `json:"connections" gorm:"type:text;default:'[]'"`  // 场景连接信息（替代connected_scenes）

	Status          string         `json:"status" gorm:"default:'active';index"`            // active, locked, hidden
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 关联
	World      World       `json:"world,omitempty" gorm:"foreignKey:WorldID"`
	Characters []Character `json:"characters,omitempty" gorm:"foreignKey:CurrentSceneID"`
	Entities   []Entity    `json:"entities,omitempty" gorm:"foreignKey:CurrentSceneID"`
	Events     []Event     `json:"events,omitempty" gorm:"foreignKey:SceneID"`      // 保留向后兼容性
	GameEvents []GameEvent `json:"game_events,omitempty" gorm:"foreignKey:SceneID"` // 新增：游戏事件
}

// UUIDArray UUID数组类型
type UUIDArray []uuid.UUID

// Scan 实现Scanner接口
func (ua *UUIDArray) Scan(value interface{}) error {
	if value == nil {
		*ua = UUIDArray{}
		return nil
	}

	var strArray []string
	switch v := value.(type) {
	case []byte:
		if err := json.Unmarshal(v, &strArray); err != nil {
			return err
		}
	case string:
		if err := json.Unmarshal([]byte(v), &strArray); err != nil {
			return err
		}
	default:
		*ua = UUIDArray{}
		return nil
	}

	// 转换字符串数组为UUID数组
	result := make(UUIDArray, 0, len(strArray))
	for _, str := range strArray {
		if id, err := uuid.Parse(str); err == nil {
			result = append(result, id)
		}
	}
	*ua = result
	return nil
}

// Value 实现Valuer接口
func (ua UUIDArray) Value() (driver.Value, error) {
	if len(ua) == 0 {
		return "[]", nil
	}

	// 转换UUID数组为字符串数组
	strArray := make([]string, len(ua))
	for i, id := range ua {
		strArray[i] = id.String()
	}

	return json.Marshal(strArray)
}

// SceneEnvironment 场景环境结构
type SceneEnvironment struct {
	Weather     string                 `json:"weather"`     // 天气
	Temperature int                    `json:"temperature"` // 温度
	Lighting    string                 `json:"lighting"`    // 光照
	Sounds      []string               `json:"sounds"`      // 声音
	Smells      []string               `json:"smells"`      // 气味
	Atmosphere  string                 `json:"atmosphere"`  // 氛围
	Hazards     []string               `json:"hazards"`     // 危险因素
	Resources   map[string]interface{} `json:"resources"`   // 可用资源
}

// TableName 指定表名
func (Scene) TableName() string {
	return "scenes"
}

// BeforeCreate GORM钩子 - 创建前
func (s *Scene) BeforeCreate(tx *gorm.DB) error {
	if s.ID == "" {
		s.ID = uuid.New().String()
	}

	// 设置默认环境
	if len(s.Environment) == 0 {
		s.Environment = JSON{
			"weather":     "clear",
			"temperature": 20,
			"lighting":    "normal",
			"sounds":      []string{},
			"smells":      []string{},
			"atmosphere":  "neutral",
			"hazards":     []string{},
			"resources":   map[string]interface{}{},
		}
	}

	return nil
}

// IsActive 检查场景是否活跃
func (s *Scene) IsActive() bool {
	return s.Status == "active"
}

// IsLocked 检查场景是否被锁定
func (s *Scene) IsLocked() bool {
	return s.Status == "locked"
}

// IsHidden 检查场景是否隐藏
func (s *Scene) IsHidden() bool {
	return s.Status == "hidden" || s.SceneType == "hidden"
}

// AddEntity 添加实体到场景
func (s *Scene) AddEntity(tx *gorm.DB, entityID string) error {
	// 检查是否已存在
	for _, id := range s.EntitiesPresent {
		if id == entityID {
			return nil
		}
	}

	s.EntitiesPresent = append(s.EntitiesPresent, entityID)
	return tx.Model(s).Update("entities_present", s.EntitiesPresent).Error
}

// RemoveEntity 从场景移除实体
func (s *Scene) RemoveEntity(tx *gorm.DB, entityID string) error {
	filtered := make(StringArray, 0, len(s.EntitiesPresent))
	for _, id := range s.EntitiesPresent {
		if id != entityID {
			filtered = append(filtered, id)
		}
	}

	s.EntitiesPresent = filtered
	return tx.Model(s).Update("entities_present", s.EntitiesPresent).Error
}

// HasEntity 检查场景是否包含指定实体
func (s *Scene) HasEntity(entityID string) bool {
	for _, id := range s.EntitiesPresent {
		if id == entityID {
			return true
		}
	}
	return false
}

// ConnectScene 连接到另一个场景
func (s *Scene) ConnectScene(tx *gorm.DB, direction string, targetSceneID string) error {
	connections := s.GetOldConnections()
	connections[direction] = targetSceneID

	// 转换 map[string]string 到 JSON (map[string]interface{})
	jsonConnections := make(JSON)
	for k, v := range connections {
		jsonConnections[k] = v
	}
	s.ConnectedScenes = jsonConnections
	return tx.Model(s).Update("connected_scenes", s.ConnectedScenes).Error
}

// DisconnectScene 断开与另一个场景的连接
func (s *Scene) DisconnectScene(tx *gorm.DB, direction string) error {
	connections := s.GetOldConnections()
	delete(connections, direction)

	// 转换 map[string]string 到 JSON (map[string]interface{})
	jsonConnections := make(JSON)
	for k, v := range connections {
		jsonConnections[k] = v
	}
	s.ConnectedScenes = jsonConnections
	return tx.Model(s).Update("connected_scenes", s.ConnectedScenes).Error
}

// GetOldConnections 获取旧格式的场景连接（向后兼容）
func (s *Scene) GetOldConnections() map[string]string {
	connections := make(map[string]string)
	for key, value := range s.ConnectedScenes {
		if str, ok := value.(string); ok {
			connections[key] = str
		}
	}
	return connections
}

// GetConnectedSceneID 获取指定方向的连接场景ID
func (s *Scene) GetConnectedSceneID(direction string) *uuid.UUID {
	connections := s.GetOldConnections()
	if sceneIDStr, ok := connections[direction]; ok {
		if sceneID, err := uuid.Parse(sceneIDStr); err == nil {
			return &sceneID
		}
	}
	return nil
}

// GetAvailableDirections 获取可用的移动方向
func (s *Scene) GetAvailableDirections() []string {
	connections := s.GetOldConnections()
	directions := make([]string, 0, len(connections))
	for direction := range connections {
		directions = append(directions, direction)
	}
	return directions
}

// UpdateEnvironment 更新环境信息
func (s *Scene) UpdateEnvironment(tx *gorm.DB, updates map[string]interface{}) error {
	// 合并更新
	for key, value := range updates {
		s.Environment[key] = value
	}

	return tx.Model(s).Update("environment", s.Environment).Error
}

// GetEnvironmentValue 获取环境值
func (s *Scene) GetEnvironmentValue(key string) interface{} {
	return s.Environment[key]
}

// SetProperty 设置场景属性
func (s *Scene) SetProperty(tx *gorm.DB, key string, value interface{}) error {
	s.Properties[key] = value
	return tx.Model(s).Update("properties", s.Properties).Error
}

// GetProperty 获取场景属性
func (s *Scene) GetProperty(key string) interface{} {
	return s.Properties[key]
}

// HasProperty 检查是否有指定属性
func (s *Scene) HasProperty(key string) bool {
	_, exists := s.Properties[key]
	return exists
}

// GetWeather 获取天气
func (s *Scene) GetWeather() string {
	if weather, ok := s.Environment["weather"].(string); ok {
		return weather
	}
	return "clear"
}

// GetTemperature 获取温度
func (s *Scene) GetTemperature() int {
	if temp, ok := s.Environment["temperature"].(float64); ok {
		return int(temp)
	}
	return 20
}

// GetLighting 获取光照
func (s *Scene) GetLighting() string {
	if lighting, ok := s.Environment["lighting"].(string); ok {
		return lighting
	}
	return "normal"
}

// GetAtmosphere 获取氛围
func (s *Scene) GetAtmosphere() string {
	if atmosphere, ok := s.Environment["atmosphere"].(string); ok {
		return atmosphere
	}
	return "neutral"
}

// IsSpecial 检查是否为特殊场景
func (s *Scene) IsSpecial() bool {
	return s.SceneType == "special"
}

// CanEnter 检查是否可以进入场景
func (s *Scene) CanEnter() bool {
	return s.IsActive() && !s.IsLocked()
}

// GetTags 获取场景标签列表
func (s *Scene) GetTags() []string {
	if len(s.Tags) > 0 {
		return []string(s.Tags)
	}

	// 回退到旧的scene_type字段
	if s.SceneType != "" {
		return []string{s.SceneType}
	}

	return []string{}
}

// HasTag 检查是否包含指定标签
func (s *Scene) HasTag(tag string) bool {
	tags := s.GetTags()
	for _, t := range tags {
		if t == tag {
			return true
		}
	}
	return false
}

// AddTag 添加场景标签
func (s *Scene) AddTag(tag string) {
	// 检查是否已存在
	for _, t := range s.Tags {
		if t == tag {
			return // 已存在，无需添加
		}
	}

	// 添加新标签
	s.Tags = append(s.Tags, tag)
}

// GetAccessRules 获取访问规则
func (s *Scene) GetAccessRules() map[string]interface{} {
	if len(s.AccessRules) == 0 {
		// 返回默认访问规则
		return map[string]interface{}{
			"visibility": "public",
			"entry_requirements": []interface{}{},
			"capacity": map[string]interface{}{
				"description": "这里可以容纳很多人",
				"soft_limit":  20,
			},
		}
	}

	return map[string]interface{}(s.AccessRules)
}

// UpdateAccessRules 更新访问规则
func (s *Scene) UpdateAccessRules(rules map[string]interface{}) {
	if s.AccessRules == nil {
		s.AccessRules = make(JSON)
	}

	for key, value := range rules {
		s.AccessRules[key] = value
	}
}

// GetConnections 获取场景连接信息
func (s *Scene) GetConnections() []map[string]interface{} {
	var connections []map[string]interface{}

	// 优先使用新的connections字段
	if len(s.Connections) > 0 {
		connectionsData := map[string]interface{}(s.Connections)
		if connectionsArray, ok := connectionsData["connections"].([]interface{}); ok {
			for _, conn := range connectionsArray {
				if connMap, ok := conn.(map[string]interface{}); ok {
					connections = append(connections, connMap)
				}
			}
			return connections
		}
	}

	// 回退到旧的connected_scenes字段
	if len(s.ConnectedScenes) > 0 {
		connectedScenes := map[string]interface{}(s.ConnectedScenes)
		connections = make([]map[string]interface{}, 0)

		for direction, sceneID := range connectedScenes {
			if sceneIDStr, ok := sceneID.(string); ok {
				connections = append(connections, map[string]interface{}{
					"id":             uuid.New().String(),
					"target_scene_id": sceneIDStr,
					"connection_type": "bidirectional",
					"direction": map[string]interface{}{
						"from_description":   "向" + direction + "走",
						"compass_direction":  direction,
					},
					"travel": map[string]interface{}{
						"description":     "一条通往" + direction + "的路径",
						"difficulty":      "easy",
						"time_description": "几分钟的路程",
					},
				})
			}
		}
	}

	return connections
}

// GetConnectionTo 获取到指定场景的连接信息
func (s *Scene) GetConnectionTo(targetSceneID string) map[string]interface{} {
	connections := s.GetConnections()

	for _, conn := range connections {
		if targetID, ok := conn["target_scene_id"].(string); ok && targetID == targetSceneID {
			return conn
		}
	}

	return nil
}

// CanAccessScene 检查是否可以访问场景（基于访问规则）
func (s *Scene) CanAccessScene(characterID string) bool {
	accessRules := s.GetAccessRules()

	// 检查可见性
	if visibility, ok := accessRules["visibility"].(string); ok && visibility == "hidden" {
		return false
	}

	// 检查进入要求
	if requirements, ok := accessRules["entry_requirements"].([]interface{}); ok && len(requirements) > 0 {
		// 这里可以添加具体的要求检查逻辑
		// 暂时返回true，实际实现需要根据具体要求进行检查
		return true
	}

	return true
}

// GetEntityCount 获取场景中实体数量
func (s *Scene) GetEntityCount() int {
	return len(s.EntitiesPresent)
}
