package models

import (
	"time"
	"gorm.io/gorm"
)

// SpecializedEntity 专门实体模型 - 兼容SQLite和PostgreSQL
// 为不同类型的实体提供特定的属性扩展
type SpecializedEntity struct {
	// 基础字段（使用string类型兼容SQLite）
	EntityID           string `gorm:"primaryKey;type:text" json:"entity_id"`                        // 关联的实体ID
	SpecializationType string `gorm:"type:varchar(50);not null;index" json:"specialization_type"` // 专门化类型

	// 专门属性（中频使用）
	SpecializedData JSON `gorm:"type:text;not null;default:'{}'" json:"specialized_data"` // 类型特定的数据

	// 时间戳
	CreatedAt time.Time `gorm:"default:(datetime('now'))" json:"created_at"`
	UpdatedAt time.Time `gorm:"default:(datetime('now'))" json:"updated_at"`

	// 关联关系
	Entity Entity `gorm:"foreignKey:EntityID;constraint:OnDelete:CASCADE" json:"entity,omitempty"`
}

// TableName 指定表名
func (SpecializedEntity) TableName() string {
	return "specialized_entities"
}

// BeforeCreate GORM钩子：创建前设置默认值
func (se *SpecializedEntity) BeforeCreate(tx *gorm.DB) error {
	// 根据专门化类型设置默认数据结构
	if len(se.SpecializedData) == 0 {
		switch se.SpecializationType {
		case "item":
			se.SpecializedData = JSON{
				"item_type": "misc",
				"rarity":    "common",
				"durability": map[string]interface{}{
					"current": 100,
					"maximum": 100,
				},
				"usage": map[string]interface{}{
					"consumable": false,
					"stackable":  true,
					"max_stack":  99,
				},
				"effects": map[string]interface{}{
					"on_use":  []interface{}{},
					"passive": []interface{}{},
				},
				"requirements": map[string]interface{}{
					"level":  1,
					"skills": []interface{}{},
				},
			}
		case "event":
			se.SpecializedData = JSON{
				"event_category": "general",
				"triggers":       []interface{}{},
				"conditions":     map[string]interface{}{},
				"outcomes":       []interface{}{},
				"repeatable":     true,
				"cooldown":       0,
			}
		case "goal":
			se.SpecializedData = JSON{
				"goal_type":    "task",
				"priority":     "normal",
				"requirements": []interface{}{},
				"rewards":      []interface{}{},
				"deadline":     nil,
				"progress": map[string]interface{}{
					"current": 0,
					"target":  1,
				},
			}
		case "location":
			se.SpecializedData = JSON{
				"location_type":  "area",
				"accessibility":  "public",
				"capacity": map[string]interface{}{
					"current": 0,
					"maximum": 100,
				},
				"features": []interface{}{},
				"services": []interface{}{},
			}
		case "abstract":
			se.SpecializedData = JSON{
				"concept_type":      "idea",
				"complexity":        "simple",
				"related_concepts":  []interface{}{},
				"manifestations":    []interface{}{},
			}
		default:
			se.SpecializedData = JSON{}
		}
	}

	return nil
}

// GetItemData 获取物品特定数据（当类型为item时）
func (se *SpecializedEntity) GetItemData() map[string]interface{} {
	if se.SpecializationType != "item" {
		return nil
	}

	return map[string]interface{}(se.SpecializedData)
}

// GetEventData 获取事件特定数据（当类型为event时）
func (se *SpecializedEntity) GetEventData() map[string]interface{} {
	if se.SpecializationType != "event" {
		return nil
	}

	return map[string]interface{}(se.SpecializedData)
}

// GetGoalData 获取目标特定数据（当类型为goal时）
func (se *SpecializedEntity) GetGoalData() map[string]interface{} {
	if se.SpecializationType != "goal" {
		return nil
	}

	return map[string]interface{}(se.SpecializedData)
}

// GetLocationData 获取地点特定数据（当类型为location时）
func (se *SpecializedEntity) GetLocationData() map[string]interface{} {
	if se.SpecializationType != "location" {
		return nil
	}

	return map[string]interface{}(se.SpecializedData)
}

// GetAbstractData 获取抽象概念特定数据（当类型为abstract时）
func (se *SpecializedEntity) GetAbstractData() map[string]interface{} {
	if se.SpecializationType != "abstract" {
		return nil
	}

	return map[string]interface{}(se.SpecializedData)
}

// UpdateSpecializedData 更新专门化数据
func (se *SpecializedEntity) UpdateSpecializedData(data map[string]interface{}) {
	if se.SpecializedData == nil {
		se.SpecializedData = make(JSON)
	}

	for key, value := range data {
		se.SpecializedData[key] = value
	}
}