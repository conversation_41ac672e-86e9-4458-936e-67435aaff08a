package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User 用户模型 - 兼容SQLite和PostgreSQL
type User struct {
	// 使用string类型存储UUID，兼容SQLite
	ID               string         `json:"id" gorm:"primaryKey;type:text"`
	ExternalID       string         `json:"external_id" gorm:"not null;index"`
	ExternalProvider string         `json:"external_provider" gorm:"not null;index"`
	Email            string         `json:"email" gorm:"not null;index"`
	DisplayName      *string        `json:"display_name"`
	AvatarURL        *string        `json:"avatar_url"`

	// 新增字段（v4.0架构）
	Profile      JSON       `json:"profile" gorm:"type:text;default:'{}'"`        // 用户档案信息（合并显示名称、头像等）
	LastActiveAt *time.Time `json:"last_active_at,omitempty" gorm:"index"`        // 用户最后活跃时间

	// 使用text类型存储JSON，兼容SQLite
	GameRoles        StringArray    `json:"game_roles" gorm:"type:text;default:'[\"user\"]'"`
	Status           string         `json:"status" gorm:"default:'active';index"`
	Preferences      JSON           `json:"preferences" gorm:"type:text;default:'{}'"`
	IDPClaims        JSON           `json:"idp_claims" gorm:"type:text;default:'{}'"`
	CreatedAt        time.Time      `json:"created_at" gorm:"index"`
	UpdatedAt        time.Time      `json:"updated_at"`
	LastLoginAt      *time.Time     `json:"last_login_at"`
	DeletedAt        gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 关联
	Stats      *UserStats    `json:"stats,omitempty" gorm:"foreignKey:UserID"`
	Worlds     []World       `json:"worlds,omitempty" gorm:"foreignKey:CreatorID"`
	Characters []Character   `json:"characters,omitempty" gorm:"foreignKey:UserID"`
	Sessions   []UserSession `json:"sessions,omitempty" gorm:"foreignKey:UserID"` // 新增：用户会话
}

// UserStats 用户统计信息模型 - 兼容SQLite和PostgreSQL
type UserStats struct {
	UserID        string      `json:"user_id" gorm:"primaryKey;type:text"`
	TotalPlayTime int         `json:"total_play_time" gorm:"default:0"`            // 总游戏时间(分钟)
	WorldsCreated int         `json:"worlds_created" gorm:"default:0"`             // 创建的世界数量
	WorldsJoined  int         `json:"worlds_joined" gorm:"default:0"`              // 加入的世界数量
	Achievements  StringArray `json:"achievements" gorm:"type:text;default:'[]'"` // 成就列表
	Level         int         `json:"level" gorm:"default:1;index"`                // 用户等级
	Experience    int         `json:"experience" gorm:"default:0;index"`           // 经验值
	UpdatedAt     time.Time   `json:"updated_at"`

	// 关联
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// StringArray 字符串数组类型，用于JSONB字段
type StringArray []string

// Scan 实现Scanner接口
func (sa *StringArray) Scan(value interface{}) error {
	if value == nil {
		*sa = StringArray{}
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, sa)
	case string:
		return json.Unmarshal([]byte(v), sa)
	default:
		return json.Unmarshal([]byte("[]"), sa)
	}
}

// Value 实现Valuer接口
func (sa StringArray) Value() (driver.Value, error) {
	if len(sa) == 0 {
		return "[]", nil
	}
	return json.Marshal(sa)
}

// JSON 通用JSON类型
type JSON map[string]interface{}

// Scan 实现Scanner接口
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		*j = JSON{}
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, j)
	case string:
		return json.Unmarshal([]byte(v), j)
	default:
		return json.Unmarshal([]byte("{}"), j)
	}
}

// Value 实现Valuer接口
func (j JSON) Value() (driver.Value, error) {
	if len(j) == 0 {
		return "{}", nil
	}
	return json.Marshal(j)
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// TableName 指定表名
func (UserStats) TableName() string {
	return "user_stats"
}

// BeforeCreate GORM钩子 - 创建前生成UUID
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == "" {
		u.ID = uuid.New().String()
	}
	return nil
}

// AfterCreate GORM钩子 - 创建后
func (u *User) AfterCreate(tx *gorm.DB) error {
	// 创建用户统计记录
	stats := &UserStats{
		UserID: u.ID,
	}
	return tx.Create(stats).Error
}

// IsActive 检查用户是否活跃
func (u *User) IsActive() bool {
	return u.Status == "active"
}

// HasRole 检查用户是否有指定角色
func (u *User) HasRole(role string) bool {
	for _, r := range u.GameRoles {
		if r == role {
			return true
		}
	}
	return false
}

// IsAdmin 检查用户是否为管理员
func (u *User) IsAdmin() bool {
	return u.HasRole("admin")
}

// IsPremium 检查用户是否为高级用户
func (u *User) IsPremium() bool {
	return u.HasRole("premium")
}

// UpdateLastLogin 更新最后登录时间
func (u *User) UpdateLastLogin(tx *gorm.DB) error {
	now := time.Now()
	u.LastLoginAt = &now
	return tx.Model(u).Update("last_login_at", now).Error
}

// AddExperience 增加经验值
func (us *UserStats) AddExperience(tx *gorm.DB, exp int) error {
	us.Experience += exp

	// 检查是否升级
	newLevel := us.calculateLevel(us.Experience)
	if newLevel > us.Level {
		us.Level = newLevel
	}

	return tx.Save(us).Error
}

// calculateLevel 根据经验值计算等级
func (us *UserStats) calculateLevel(experience int) int {
	// 简单的等级计算公式：每1000经验值升1级
	return (experience / 1000) + 1
}

// AddAchievement 添加成就
func (us *UserStats) AddAchievement(tx *gorm.DB, achievement string) error {
	// 检查是否已有该成就
	for _, a := range us.Achievements {
		if a == achievement {
			return nil // 已有该成就
		}
	}

	us.Achievements = append(us.Achievements, achievement)
	return tx.Save(us).Error
}

// IncrementWorldsCreated 增加创建世界数量
func (us *UserStats) IncrementWorldsCreated(tx *gorm.DB) error {
	us.WorldsCreated++
	return tx.Model(us).Update("worlds_created", us.WorldsCreated).Error
}

// IncrementWorldsJoined 增加加入世界数量
func (us *UserStats) IncrementWorldsJoined(tx *gorm.DB) error {
	us.WorldsJoined++
	return tx.Model(us).Update("worlds_joined", us.WorldsJoined).Error
}

// AddPlayTime 增加游戏时间
func (us *UserStats) AddPlayTime(tx *gorm.DB, minutes int) error {
	us.TotalPlayTime += minutes
	return tx.Model(us).Update("total_play_time", us.TotalPlayTime).Error
}

// GetDisplayName 获取显示名称（优先从profile获取）
func (u *User) GetDisplayName() string {
	// 首先尝试从profile字段获取
	if displayName, ok := u.Profile["display_name"].(string); ok && displayName != "" {
		return displayName
	}

	// 回退到原始字段
	if u.DisplayName != nil && *u.DisplayName != "" {
		return *u.DisplayName
	}

	// 最后回退到邮箱
	return u.Email
}

// GetAvatarURL 获取头像URL（优先从profile获取）
func (u *User) GetAvatarURL() string {
	// 首先尝试从profile字段获取
	if avatar, ok := u.Profile["avatar"].(map[string]interface{}); ok {
		if url, ok := avatar["url"].(string); ok && url != "" {
			return url
		}
	}

	// 回退到原始字段
	if u.AvatarURL != nil {
		return *u.AvatarURL
	}

	return ""
}

// UpdateProfile 更新用户档案信息
func (u *User) UpdateProfile(displayName, avatarURL, locale, timezone string) {
	if u.Profile == nil {
		u.Profile = make(JSON)
	}

	// 更新显示名称
	if displayName != "" {
		u.Profile["display_name"] = displayName
	}

	// 更新头像信息
	if avatarURL != "" {
		avatar := map[string]interface{}{
			"url":    avatarURL,
			"source": "external",
		}
		u.Profile["avatar"] = avatar
	}

	// 更新语言和时区
	if locale != "" {
		u.Profile["locale"] = locale
	}
	if timezone != "" {
		u.Profile["timezone"] = timezone
	}

	// 设置UI偏好（如果不存在）
	if _, exists := u.Profile["ui_preferences"]; !exists {
		u.Profile["ui_preferences"] = map[string]interface{}{
			"theme":         "auto",
			"language":      locale,
			"notifications": true,
		}
	}
}

// UpdateLastActive 更新最后活跃时间
func (u *User) UpdateLastActive(tx *gorm.DB) error {
	now := time.Now()
	u.LastActiveAt = &now
	return tx.Model(u).Update("last_active_at", now).Error
}

// GetLocale 获取用户语言偏好
func (u *User) GetLocale() string {
	if locale, ok := u.Profile["locale"].(string); ok && locale != "" {
		return locale
	}
	return "zh-CN" // 默认中文
}

// GetTimezone 获取用户时区
func (u *User) GetTimezone() string {
	if timezone, ok := u.Profile["timezone"].(string); ok && timezone != "" {
		return timezone
	}
	return "Asia/Shanghai" // 默认上海时区
}


