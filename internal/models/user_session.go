package models

import (
	"time"
	"gorm.io/gorm"
)

// UserSession 用户会话模型 - 兼容SQLite和PostgreSQL
// 管理用户在不同世界中的会话状态，包括当前活跃角色、UI状态等
type UserSession struct {
	// 基础字段（使用string类型兼容SQLite）
	ID     string `gorm:"primaryKey;type:text" json:"id"`
	UserID string `gorm:"type:text;not null;index" json:"user_id"`
	WorldID string `gorm:"type:text;not null;index" json:"world_id"`

	// 会话状态（高频使用）
	ActiveCharacterID *string `gorm:"type:text;index" json:"active_character_id,omitempty"` // 当前活跃角色（软引用）
	SessionData       JSON    `gorm:"type:text;not null;default:'{}'" json:"session_data"`  // 会话相关数据

	// 时间管理
	CreatedAt        time.Time  `gorm:"default:(datetime('now'))" json:"created_at"`
	UpdatedAt        time.Time  `gorm:"default:(datetime('now'))" json:"updated_at"`
	LastActivityAt   time.Time  `gorm:"default:(datetime('now'));index" json:"last_activity_at"`

	// 关联关系
	User   User   `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE" json:"user,omitempty"`
	World  World  `gorm:"foreignKey:WorldID;constraint:OnDelete:CASCADE" json:"world,omitempty"`
	ActiveCharacter *Character `gorm:"foreignKey:ActiveCharacterID" json:"active_character,omitempty"`
}

// TableName 指定表名
func (UserSession) TableName() string {
	return "user_sessions"
}

// BeforeCreate GORM钩子：创建前设置默认值
func (us *UserSession) BeforeCreate(tx *gorm.DB) error {
	if us.ID == "" {
		us.ID = generateUUID()
	}

	// 设置默认会话数据
	if len(us.SessionData) == 0 {
		us.SessionData = JSON{
			"ui_state": map[string]interface{}{
				"current_view":      "world",
				"sidebar_collapsed": false,
				"chat_panel_open":   true,
			},
			"game_state": map[string]interface{}{
				"tutorial_completed": false,
				"last_scene_id":      nil,
				"inventory_open":     false,
			},
			"preferences": map[string]interface{}{
				"auto_save":        true,
				"sound_enabled":    true,
				"animation_speed":  "normal",
			},
		}
	}

	return nil
}

// BeforeUpdate GORM钩子：更新前更新活跃时间
func (us *UserSession) BeforeUpdate(tx *gorm.DB) error {
	us.LastActivityAt = time.Now()
	return nil
}

// IsActive 检查会话是否活跃（最近24小时内有活动）
func (us *UserSession) IsActive() bool {
	return time.Since(us.LastActivityAt) < 24*time.Hour
}

// GetUIState 获取UI状态
func (us *UserSession) GetUIState() map[string]interface{} {
	sessionData := map[string]interface{}(us.SessionData)

	if uiState, ok := sessionData["ui_state"].(map[string]interface{}); ok {
		return uiState
	}

	return map[string]interface{}{}
}

// SetUIState 设置UI状态
func (us *UserSession) SetUIState(uiState map[string]interface{}) {
	if us.SessionData == nil {
		us.SessionData = make(JSON)
	}

	sessionData := map[string]interface{}(us.SessionData)
	sessionData["ui_state"] = uiState
	us.SessionData = JSON(sessionData)
}

// GetGameState 获取游戏状态
func (us *UserSession) GetGameState() map[string]interface{} {
	sessionData := map[string]interface{}(us.SessionData)

	if gameState, ok := sessionData["game_state"].(map[string]interface{}); ok {
		return gameState
	}

	return map[string]interface{}{}
}

// SetGameState 设置游戏状态
func (us *UserSession) SetGameState(gameState map[string]interface{}) {
	if us.SessionData == nil {
		us.SessionData = make(JSON)
	}

	sessionData := map[string]interface{}(us.SessionData)
	sessionData["game_state"] = gameState
	us.SessionData = JSON(sessionData)
}

// generateUUID 生成UUID字符串（兼容函数）
func generateUUID() string {
	// 这里使用简单的UUID生成，实际项目中可能需要更复杂的实现
	return time.Now().Format("20060102150405") + "-" + "session"
}