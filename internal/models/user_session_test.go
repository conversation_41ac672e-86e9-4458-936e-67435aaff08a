package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB 设置测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// 自动迁移表结构
	err = db.AutoMigrate(&User{}, &UserStats{}, &World{}, &Character{}, &UserSession{})
	require.NoError(t, err)

	return db
}

func TestUserSession_BeforeCreate(t *testing.T) {
	db := setupTestDB(t)

	session := &UserSession{
		UserID:  "user-123",
		WorldID: "world-456",
	}

	err := db.Create(session).Error
	require.NoError(t, err)

	// 检查ID是否自动生成
	assert.NotEmpty(t, session.ID)

	// 检查默认会话数据是否设置
	assert.NotNil(t, session.SessionData)

	uiState := session.GetUIState()
	assert.Equal(t, "world", uiState["current_view"])
	assert.Equal(t, false, uiState["sidebar_collapsed"])
	assert.Equal(t, true, uiState["chat_panel_open"])

	gameState := session.GetGameState()
	assert.Equal(t, false, gameState["tutorial_completed"])
	assert.Nil(t, gameState["last_scene_id"])
	assert.Equal(t, false, gameState["inventory_open"])
}

func TestUserSession_IsActive(t *testing.T) {
	session := &UserSession{
		LastActivityAt: time.Now().Add(-1 * time.Hour), // 1小时前
	}

	// 1小时前的活动应该仍然是活跃的
	assert.True(t, session.IsActive())

	// 25小时前的活动应该不是活跃的
	session.LastActivityAt = time.Now().Add(-25 * time.Hour)
	assert.False(t, session.IsActive())
}

func TestUserSession_UIStateOperations(t *testing.T) {
	session := &UserSession{
		SessionData: JSON{
			"ui_state": map[string]interface{}{
				"current_view": "character",
				"theme":        "dark",
			},
		},
	}

	// 测试获取UI状态
	uiState := session.GetUIState()
	assert.Equal(t, "character", uiState["current_view"])
	assert.Equal(t, "dark", uiState["theme"])

	// 测试设置UI状态
	newUIState := map[string]interface{}{
		"current_view":      "inventory",
		"sidebar_collapsed": true,
		"theme":             "light",
	}
	session.SetUIState(newUIState)

	updatedUIState := session.GetUIState()
	assert.Equal(t, "inventory", updatedUIState["current_view"])
	assert.Equal(t, true, updatedUIState["sidebar_collapsed"])
	assert.Equal(t, "light", updatedUIState["theme"])
}

func TestUserSession_GameStateOperations(t *testing.T) {
	session := &UserSession{
		SessionData: JSON{
			"game_state": map[string]interface{}{
				"tutorial_completed": true,
				"current_level":      5,
			},
		},
	}

	// 测试获取游戏状态
	gameState := session.GetGameState()
	assert.Equal(t, true, gameState["tutorial_completed"])
	assert.Equal(t, 5, gameState["current_level"]) // 直接设置的值应该保持原类型

	// 测试设置游戏状态
	newGameState := map[string]interface{}{
		"tutorial_completed": true,
		"current_level":      10,
		"last_scene_id":      "scene-789",
	}
	session.SetGameState(newGameState)

	updatedGameState := session.GetGameState()
	assert.Equal(t, true, updatedGameState["tutorial_completed"])
	assert.Equal(t, 10, updatedGameState["current_level"])
	assert.Equal(t, "scene-789", updatedGameState["last_scene_id"])
}

func TestUserSession_DatabaseOperations(t *testing.T) {
	db := setupTestDB(t)

	// 创建测试用户和世界
	user := &User{
		ID:               "user-123",
		ExternalID:       "ext-123",
		ExternalProvider: "test",
		Email:            "<EMAIL>",
	}
	err := db.Create(user).Error
	require.NoError(t, err)

	world := &World{
		ID:        "world-456",
		Name:      "测试世界",
		CreatorID: "user-123",
		WorldConfig: JSON{
			"theme": "fantasy",
		},
		WorldState: JSON{
			"time": 0,
		},
	}
	err = db.Create(world).Error
	require.NoError(t, err)

	// 创建用户会话
	session := &UserSession{
		UserID:  "user-123",
		WorldID: "world-456",
	}
	err = db.Create(session).Error
	require.NoError(t, err)

	// 测试查询会话
	var foundSession UserSession
	err = db.Where("user_id = ? AND world_id = ?", "user-123", "world-456").First(&foundSession).Error
	require.NoError(t, err)

	assert.Equal(t, session.ID, foundSession.ID)
	assert.Equal(t, "user-123", foundSession.UserID)
	assert.Equal(t, "world-456", foundSession.WorldID)

	// 测试唯一约束
	duplicateSession := &UserSession{
		UserID:  "user-123",
		WorldID: "world-456",
	}
	err = db.Create(duplicateSession).Error
	assert.Error(t, err) // 应该违反唯一约束
}