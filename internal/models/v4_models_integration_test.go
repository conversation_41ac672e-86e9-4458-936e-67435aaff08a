package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// TestV4ModelsIntegration 测试v4.0架构的所有新模型的集成
func TestV4ModelsIntegration(t *testing.T) {
	// 设置测试数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// 自动迁移所有模型
	err = db.AutoMigrate(
		&User{}, &UserStats{}, &World{}, &Character{}, &Entity{}, &Scene{},
		&UserSession{}, &CharacterMemory{}, &CharacterExperience{},
		&SpecializedEntity{}, &GameEvent{},
	)
	require.NoError(t, err)

	// 创建测试数据
	user := createTestUser(t, db)
	world := createTestWorld(t, db, user.ID)
	character := createTestCharacter(t, db, user.ID, world.ID)
	entity := createTestEntity(t, db, world.ID)

	// 测试用户会话
	testUserSession(t, db, user.ID, world.ID, character.ID)

	// 测试角色记忆
	testCharacterMemory(t, db, character.ID)

	// 测试角色阅历
	testCharacterExperience(t, db, character.ID)

	// 测试专门实体
	testSpecializedEntity(t, db, entity.ID)

	// 测试游戏事件
	testGameEvent(t, db, world.ID, character.ID)

	// 测试关联查询
	testAssociationQueries(t, db, user.ID, world.ID, character.ID)
}

func createTestUser(t *testing.T, db *gorm.DB) *User {
	user := &User{
		ID:               "test-user-001",
		ExternalID:       "ext-001",
		ExternalProvider: "test",
		Email:            "<EMAIL>",
		Profile: JSON{
			"display_name": "测试用户",
			"avatar": map[string]interface{}{
				"url":    "https://example.com/avatar.jpg",
				"source": "external",
			},
			"locale":   "zh-CN",
			"timezone": "Asia/Shanghai",
		},
	}

	err := db.Create(user).Error
	require.NoError(t, err)

	return user
}

func createTestWorld(t *testing.T, db *gorm.DB, creatorID string) *World {
	world := &World{
		ID:        "test-world-001",
		Name:      "测试世界",
		CreatorID: creatorID,
		WorldConfig: JSON{
			"theme": map[string]interface{}{
				"genre":      "fantasy",
				"atmosphere": "mysterious",
			},
		},
		WorldState: JSON{
			"time":    0,
			"weather": "sunny",
		},
		AccessSettings: JSON{
			"is_public":    true,
			"max_players":  10,
			"join_policy":  "open",
		},
		Tags: StringArray{"fantasy", "adventure"},
		TimeConfig: JSON{
			"time_multiplier":    1.0,
			"pause_when_empty":   true,
			"day_night_cycle":    true,
			"season_length_days": 30,
		},
	}

	err := db.Create(world).Error
	require.NoError(t, err)

	return world
}

func createTestCharacter(t *testing.T, db *gorm.DB, userID, worldID string) *Character {
	character := &Character{
		ID:            "test-char-001",
		UserID:        &userID,
		WorldID:       worldID,
		Name:          "测试角色",
		CharacterType: "player",
		Characteristics: JSON{
			"traits": map[string]interface{}{
				"勇敢": map[string]interface{}{
					"name":         "勇敢",
					"category":     "personality",
					"intensity":    0.8,
					"description":  "面对危险时表现出勇气",
				},
			},
			"appearance": map[string]interface{}{
				"description": "一个年轻的冒险者",
				"distinctive_features": map[string]interface{}{
					"eyes":       "明亮的眼睛",
					"expression": "坚定的表情",
				},
			},
		},
		IsPrimary:    true,
		DisplayOrder: 0,
	}

	err := db.Create(character).Error
	require.NoError(t, err)

	return character
}

func createTestEntity(t *testing.T, db *gorm.DB, worldID string) *Entity {
	entity := &Entity{
		ID:         "test-entity-001",
		WorldID:    worldID,
		Name:       "魔法剑",
		EntityType: "weapon",
		Properties: JSON{
			"damage":     50,
			"durability": 100,
			"rarity":     "rare",
		},
		Tags:    StringArray{"weapon", "magic", "sword"},
		Version: 1,
	}

	err := db.Create(entity).Error
	require.NoError(t, err)

	return entity
}

func testUserSession(t *testing.T, db *gorm.DB, userID, worldID, characterID string) {
	session := &UserSession{
		UserID:            userID,
		WorldID:           worldID,
		ActiveCharacterID: &characterID,
	}

	err := db.Create(session).Error
	require.NoError(t, err)

	// 测试会话数据操作
	session.SetUIState(map[string]interface{}{
		"current_view": "character",
		"theme":        "dark",
	})

	session.SetGameState(map[string]interface{}{
		"tutorial_completed": true,
		"current_level":      5,
	})

	err = db.Save(session).Error
	require.NoError(t, err)

	// 验证数据
	var foundSession UserSession
	err = db.First(&foundSession, "id = ?", session.ID).Error
	require.NoError(t, err)

	uiState := foundSession.GetUIState()
	assert.Equal(t, "character", uiState["current_view"])
	assert.Equal(t, "dark", uiState["theme"])

	gameState := foundSession.GetGameState()
	assert.Equal(t, true, gameState["tutorial_completed"])
	assert.Equal(t, float64(5), gameState["current_level"]) // JSON数字会被解析为float64
}

func testCharacterMemory(t *testing.T, db *gorm.DB, characterID string) {
	memory := &CharacterMemory{
		CharacterID:     characterID,
		Content:         "在森林中遇到了一只巨龙",
		MemoryType:      "event",
		ImportanceScore: 0.9,
		EmotionalImpact: 0.7,
		Tags:            StringArray{"dragon", "forest", "encounter"},
		AssociatedEntities: StringArray{"dragon-001", "forest-location-001"},
	}

	err := db.Create(memory).Error
	require.NoError(t, err)

	// 测试记忆检索
	var foundMemory CharacterMemory
	err = db.First(&foundMemory, "character_id = ? AND memory_type = ?", characterID, "event").Error
	require.NoError(t, err)

	assert.Equal(t, "在森林中遇到了一只巨龙", foundMemory.Content)
	assert.Equal(t, 0.9, foundMemory.ImportanceScore)
	assert.True(t, foundMemory.IsAccessible())

	// 测试标签操作
	tags := foundMemory.GetTags()
	assert.Contains(t, tags, "dragon")
	assert.Contains(t, tags, "forest")

	// 测试记忆强化
	originalStrength := foundMemory.CurrentStrength
	foundMemory.Reinforce(0.1)
	assert.GreaterOrEqual(t, foundMemory.CurrentStrength, originalStrength) // 强度应该增加或保持
	assert.LessOrEqual(t, foundMemory.CurrentStrength, 1.0) // 不应超过1.0
}

func testCharacterExperience(t *testing.T, db *gorm.DB, characterID string) {
	experience := &CharacterExperience{
		CharacterID: characterID,
		Tags:        StringArray{"combat", "swordsmanship"},
		Description: "剑术训练经验",
		Proficiency: JSON{
			"level":             3,
			"experience_points": 250,
			"mastery_level":     "intermediate",
			"specializations":   []interface{}{"one_handed_sword", "parrying"},
		},
	}

	err := db.Create(experience).Error
	require.NoError(t, err)

	// 测试阅历查询
	var foundExperience CharacterExperience
	err = db.First(&foundExperience, "character_id = ?", characterID).Error
	require.NoError(t, err)

	assert.Equal(t, "剑术训练经验", foundExperience.Description)
	assert.True(t, foundExperience.HasTag("combat"))
	assert.True(t, foundExperience.HasTag("swordsmanship"))

	// 测试熟练度操作
	assert.Equal(t, 3, foundExperience.GetProficiencyLevel())
	assert.Equal(t, 250, foundExperience.GetExperiencePoints())

	// 测试经验值增加
	foundExperience.AddExperience(100)
	err = db.Save(&foundExperience).Error
	require.NoError(t, err)

	// 重新查询验证
	var updatedExperience CharacterExperience
	err = db.First(&updatedExperience, "id = ?", foundExperience.ID).Error
	require.NoError(t, err)

	assert.Equal(t, 350, updatedExperience.GetExperiencePoints())
	assert.Equal(t, 4, updatedExperience.GetProficiencyLevel()) // 应该升级
}

func testSpecializedEntity(t *testing.T, db *gorm.DB, entityID string) {
	specializedEntity := &SpecializedEntity{
		EntityID:           entityID,
		SpecializationType: "item",
		SpecializedData: JSON{
			"item_type": "weapon",
			"rarity":    "legendary",
			"durability": map[string]interface{}{
				"current": 95,
				"maximum": 100,
			},
			"effects": map[string]interface{}{
				"on_use": []interface{}{
					map[string]interface{}{
						"type":        "damage_boost",
						"value":       20,
						"duration_ms": 5000,
					},
				},
			},
		},
	}

	err := db.Create(specializedEntity).Error
	require.NoError(t, err)

	// 测试专门化数据查询
	var foundSpecialized SpecializedEntity
	err = db.First(&foundSpecialized, "entity_id = ?", entityID).Error
	require.NoError(t, err)

	assert.Equal(t, "item", foundSpecialized.SpecializationType)

	itemData := foundSpecialized.GetItemData()
	assert.NotNil(t, itemData)
	assert.Equal(t, "weapon", itemData["item_type"])
	assert.Equal(t, "legendary", itemData["rarity"])

	// 测试数据更新
	foundSpecialized.UpdateSpecializedData(map[string]interface{}{
		"enchantments": []interface{}{"fire_damage", "sharpness"},
	})

	updatedData := foundSpecialized.GetItemData()
	assert.Contains(t, updatedData, "enchantments")
}

func testGameEvent(t *testing.T, db *gorm.DB, worldID, characterID string) {
	gameEvent := &GameEvent{
		WorldID:        worldID,
		EventType:      "action",
		Tags:           StringArray{"combat", "player_action"},
		PrimaryActorID: &characterID,
		NarrativeText:  "勇敢的冒险者挥舞着魔法剑攻击巨龙",
		EventData: JSON{
			"action_type": "attack",
			"target":      "dragon-001",
			"weapon":      "magic-sword-001",
			"damage":      75,
		},
		GameTime: time.Now().UnixMilli(),
	}

	err := db.Create(gameEvent).Error
	require.NoError(t, err)

	// 测试事件查询
	var foundEvent GameEvent
	err = db.First(&foundEvent, "world_id = ? AND event_type = ?", worldID, "action").Error
	require.NoError(t, err)

	assert.Equal(t, "勇敢的冒险者挥舞着魔法剑攻击巨龙", foundEvent.NarrativeText)
	assert.Equal(t, characterID, *foundEvent.PrimaryActorID)

	// 测试标签操作
	tags := foundEvent.GetTags()
	assert.Contains(t, tags, "combat")
	assert.Contains(t, tags, "player_action")

	// 测试事件数据操作
	actionType := foundEvent.GetEventDataField("action_type")
	assert.Equal(t, "attack", actionType)

	foundEvent.SetEventDataField("result", "hit")
	result := foundEvent.GetEventDataField("result")
	assert.Equal(t, "hit", result)
}

func testAssociationQueries(t *testing.T, db *gorm.DB, userID, worldID, characterID string) {
	// 测试用户关联查询
	var user User
	err := db.Preload("Sessions").First(&user, "id = ?", userID).Error
	require.NoError(t, err)

	assert.NotEmpty(t, user.Sessions)
	assert.Equal(t, worldID, user.Sessions[0].WorldID)

	// 测试世界关联查询
	var world World
	err = db.Preload("GameEvents").First(&world, "id = ?", worldID).Error
	require.NoError(t, err)

	assert.NotEmpty(t, world.GameEvents)

	// 测试角色关联查询
	var character Character
	err = db.Preload("MemoryRecords").Preload("ExperienceRecords").First(&character, "id = ?", characterID).Error
	require.NoError(t, err)

	assert.NotEmpty(t, character.MemoryRecords)
	assert.NotEmpty(t, character.ExperienceRecords)

	// 测试实体关联查询
	var entity Entity
	err = db.Preload("Specialization").First(&entity, "world_id = ?", worldID).Error
	require.NoError(t, err)

	assert.NotNil(t, entity.Specialization)
	assert.Equal(t, "item", entity.Specialization.SpecializationType)
}