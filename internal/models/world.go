package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// World 游戏世界模型 - 兼容SQLite和PostgreSQL
type World struct {
	// 使用string类型存储UUID，兼容SQLite
	ID             string         `json:"id" gorm:"primaryKey;type:text"`
	Name           string         `json:"name" gorm:"not null;size:200"`
	Description    *string        `json:"description"`
	// 使用string类型存储UUID，兼容SQLite
	CreatorID      string         `json:"creator_id" gorm:"type:text;not null;index"`
	// 使用text类型存储JSON，兼容SQLite
	WorldConfig    JSON           `json:"world_config" gorm:"type:text;not null"`
	WorldState     JSON           `json:"world_state" gorm:"type:text;not null"`

	// 新增字段（v4.0架构）
	AccessSettings JSON           `json:"access_settings" gorm:"type:text;default:'{}'"`  // 访问设置（合并is_public, max_players等）
	Tags           StringArray    `json:"tags" gorm:"type:text;default:'[]'"`             // 世界标签
	TimeConfig     JSON           `json:"time_config" gorm:"type:text;default:'{}'"`      // 时间配置

	Status         string         `json:"status" gorm:"default:'active';index"`
	IsPublic       bool           `json:"is_public" gorm:"default:false;index"`           // 保留向后兼容性
	MaxPlayers     int            `json:"max_players" gorm:"default:10"`                  // 保留向后兼容性
	CurrentPlayers int            `json:"current_players" gorm:"default:0"`
	GameTime       int64          `json:"game_time" gorm:"default:0"` // 游戏内时间(分钟)
	CreatedAt      time.Time      `json:"created_at" gorm:"index"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 关联
	Creator    User          `json:"creator,omitempty" gorm:"foreignKey:CreatorID"`
	Scenes     []Scene       `json:"scenes,omitempty" gorm:"foreignKey:WorldID"`
	Characters []Character   `json:"characters,omitempty" gorm:"foreignKey:WorldID"`
	Entities   []Entity      `json:"entities,omitempty" gorm:"foreignKey:WorldID"`
	Events     []Event       `json:"events,omitempty" gorm:"foreignKey:WorldID"`         // 保留向后兼容性
	GameEvents []GameEvent   `json:"game_events,omitempty" gorm:"foreignKey:WorldID"`   // 新增：游戏事件
	Sessions   []UserSession `json:"sessions,omitempty" gorm:"foreignKey:WorldID"`      // 新增：用户会话
}

// WorldConfig 世界配置结构
type WorldConfig struct {
	TimeRate         float64                `json:"time_rate"`           // 时间倍率
	TickInterval     int                    `json:"tick_interval"`       // 心跳间隔(秒)
	MaxMemoryPerChar int                    `json:"max_memory_per_char"` // 每个角色最大记忆数
	Rules            map[string]interface{} `json:"rules"`               // 自定义规则
	Theme            string                 `json:"theme"`               // 世界主题
	Difficulty       string                 `json:"difficulty"`          // 难度等级
	Language         string                 `json:"language"`            // 语言设置
}

// WorldState 世界状态结构
type WorldState struct {
	CurrentTick     int64                  `json:"current_tick"`     // 当前心跳数
	LastTickAt      time.Time              `json:"last_tick_at"`     // 最后心跳时间
	ActiveEvents    []uuid.UUID            `json:"active_events"`    // 活跃事件ID列表
	GlobalVariables map[string]interface{} `json:"global_variables"` // 全局变量
	Weather         map[string]interface{} `json:"weather"`          // 天气状态
	Season          string                 `json:"season"`           // 季节
	WorldGoals      []uuid.UUID            `json:"world_goals"`      // 世界目标ID列表
}

// TableName 指定表名
func (World) TableName() string {
	return "worlds"
}

// BeforeCreate GORM钩子 - 创建前
func (w *World) BeforeCreate(tx *gorm.DB) error {
	if w.ID == "" {
		w.ID = uuid.New().String()
	}

	// 设置默认配置
	if len(w.WorldConfig) == 0 {
		w.WorldConfig = JSON{
			"time_rate":           1.0,
			"tick_interval":       30,
			"max_memory_per_char": 100,
			"rules":               map[string]interface{}{},
			"theme":               "fantasy",
			"difficulty":          "normal",
			"language":            "zh-CN",
		}
	}

	// 设置默认状态
	if len(w.WorldState) == 0 {
		w.WorldState = JSON{
			"current_tick":     0,
			"last_tick_at":     time.Now(),
			"active_events":    []uuid.UUID{},
			"global_variables": map[string]interface{}{},
			"weather":          map[string]interface{}{"type": "clear", "temperature": 20},
			"season":           "spring",
			"world_goals":      []uuid.UUID{},
		}
	}

	return nil
}

// AfterCreate GORM钩子 - 创建后
func (w *World) AfterCreate(tx *gorm.DB) error {
	// 更新创建者的统计信息
	var stats UserStats
	err := tx.Where("user_id = ?", w.CreatorID).First(&stats).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果用户统计不存在，创建一个新的
			stats = UserStats{
				UserID:        w.CreatorID,
				WorldsCreated: 1,
			}
			return tx.Create(&stats).Error
		}
		return err
	}
	return stats.IncrementWorldsCreated(tx)
}

// IsActive 检查世界是否活跃
func (w *World) IsActive() bool {
	return w.Status == "active"
}

// IsFull 检查世界是否已满
func (w *World) IsFull() bool {
	return w.CurrentPlayers >= w.MaxPlayers
}

// CanJoin 检查是否可以加入世界
func (w *World) CanJoin() bool {
	return w.IsActive() && !w.IsFull()
}

// AddPlayer 添加玩家
func (w *World) AddPlayer(tx *gorm.DB) error {
	if w.IsFull() {
		return gorm.ErrInvalidData
	}

	w.CurrentPlayers++
	return tx.Model(w).Update("current_players", w.CurrentPlayers).Error
}

// RemovePlayer 移除玩家
func (w *World) RemovePlayer(tx *gorm.DB) error {
	if w.CurrentPlayers <= 0 {
		return nil
	}

	w.CurrentPlayers--
	return tx.Model(w).Update("current_players", w.CurrentPlayers).Error
}

// UpdateGameTime 更新游戏时间
func (w *World) UpdateGameTime(tx *gorm.DB, minutes int64) error {
	w.GameTime += minutes
	return tx.Model(w).Update("game_time", w.GameTime).Error
}

// GetTimeRate 获取时间倍率
func (w *World) GetTimeRate() float64 {
	if rate, ok := w.WorldConfig["time_rate"].(float64); ok {
		return rate
	}
	return 1.0
}

// GetTickInterval 获取心跳间隔
func (w *World) GetTickInterval() int {
	if interval, ok := w.WorldConfig["tick_interval"].(float64); ok {
		return int(interval)
	}
	return 30
}

// GetMaxMemoryPerChar 获取每个角色最大记忆数
func (w *World) GetMaxMemoryPerChar() int {
	if maxMemory, ok := w.WorldConfig["max_memory_per_char"].(float64); ok {
		return int(maxMemory)
	}
	return 100
}

// UpdateWorldState 更新世界状态
func (w *World) UpdateWorldState(tx *gorm.DB, updates map[string]interface{}) error {
	// 合并更新
	for key, value := range updates {
		w.WorldState[key] = value
	}

	return tx.Model(w).Update("world_state", w.WorldState).Error
}

// GetCurrentTick 获取当前心跳数
func (w *World) GetCurrentTick() int64 {
	if tick, ok := w.WorldState["current_tick"].(float64); ok {
		return int64(tick)
	}
	return 0
}

// IncrementTick 增加心跳数
func (w *World) IncrementTick(tx *gorm.DB) error {
	currentTick := w.GetCurrentTick() + 1
	updates := map[string]interface{}{
		"current_tick": currentTick,
		"last_tick_at": time.Now(),
	}
	return w.UpdateWorldState(tx, updates)
}

// AddActiveEvent 添加活跃事件
func (w *World) AddActiveEvent(tx *gorm.DB, eventID uuid.UUID) error {
	activeEvents := w.GetActiveEvents()

	// 检查是否已存在
	for _, id := range activeEvents {
		if id == eventID {
			return nil
		}
	}

	activeEvents = append(activeEvents, eventID)
	return w.UpdateWorldState(tx, map[string]interface{}{
		"active_events": activeEvents,
	})
}

// RemoveActiveEvent 移除活跃事件
func (w *World) RemoveActiveEvent(tx *gorm.DB, eventID uuid.UUID) error {
	activeEvents := w.GetActiveEvents()

	// 过滤掉指定事件
	filtered := make([]uuid.UUID, 0, len(activeEvents))
	for _, id := range activeEvents {
		if id != eventID {
			filtered = append(filtered, id)
		}
	}

	return w.UpdateWorldState(tx, map[string]interface{}{
		"active_events": filtered,
	})
}

// GetActiveEvents 获取活跃事件列表
func (w *World) GetActiveEvents() []uuid.UUID {
	if events, ok := w.WorldState["active_events"].([]interface{}); ok {
		result := make([]uuid.UUID, 0, len(events))
		for _, e := range events {
			if eventStr, ok := e.(string); ok {
				if eventID, err := uuid.Parse(eventStr); err == nil {
					result = append(result, eventID)
				}
			}
		}
		return result
	}
	return []uuid.UUID{}
}

// SetGlobalVariable 设置全局变量
func (w *World) SetGlobalVariable(tx *gorm.DB, key string, value interface{}) error {
	globalVars := w.GetGlobalVariables()
	globalVars[key] = value

	return w.UpdateWorldState(tx, map[string]interface{}{
		"global_variables": globalVars,
	})
}

// GetGlobalVariable 获取全局变量
func (w *World) GetGlobalVariable(key string) interface{} {
	globalVars := w.GetGlobalVariables()
	return globalVars[key]
}

// GetAccessSettings 获取访问设置
func (w *World) GetAccessSettings() map[string]interface{} {
	if len(w.AccessSettings) == 0 {
		// 从旧字段构建默认设置
		return map[string]interface{}{
			"is_public":    w.IsPublic,
			"max_players":  w.MaxPlayers,
			"join_policy":  map[bool]string{true: "open", false: "invite"}[w.IsPublic],
		}
	}

	return map[string]interface{}(w.AccessSettings)
}

// UpdateAccessSettings 更新访问设置
func (w *World) UpdateAccessSettings(settings map[string]interface{}) {
	if w.AccessSettings == nil {
		w.AccessSettings = make(JSON)
	}

	for key, value := range settings {
		w.AccessSettings[key] = value
	}

	// 同步到旧字段以保持兼容性
	if isPublic, ok := settings["is_public"].(bool); ok {
		w.IsPublic = isPublic
	}
	if maxPlayers, ok := settings["max_players"].(float64); ok {
		w.MaxPlayers = int(maxPlayers)
	}
}

// GetTags 获取世界标签列表
func (w *World) GetTags() []string {
	return []string(w.Tags)
}

// HasTag 检查是否包含指定标签
func (w *World) HasTag(tag string) bool {
	for _, t := range w.Tags {
		if t == tag {
			return true
		}
	}
	return false
}

// AddTag 添加世界标签
func (w *World) AddTag(tag string) {
	// 检查是否已存在
	for _, t := range w.Tags {
		if t == tag {
			return // 已存在，无需添加
		}
	}

	// 添加新标签
	w.Tags = append(w.Tags, tag)
}

// RemoveTag 移除世界标签
func (w *World) RemoveTag(tag string) {
	for i, t := range w.Tags {
		if t == tag {
			w.Tags = append(w.Tags[:i], w.Tags[i+1:]...)
			return
		}
	}
}

// GetTimeConfig 获取时间配置
func (w *World) GetTimeConfig() map[string]interface{} {
	if len(w.TimeConfig) == 0 {
		// 返回默认时间配置
		return map[string]interface{}{
			"time_multiplier":    1.0,
			"pause_when_empty":   true,
			"day_night_cycle":    true,
			"season_length_days": 30,
		}
	}

	return map[string]interface{}(w.TimeConfig)
}

// UpdateTimeConfig 更新时间配置
func (w *World) UpdateTimeConfig(config map[string]interface{}) {
	if w.TimeConfig == nil {
		w.TimeConfig = make(JSON)
	}

	for key, value := range config {
		w.TimeConfig[key] = value
	}
}

// GetTimeMultiplier 获取时间倍率
func (w *World) GetTimeMultiplier() float64 {
	timeConfig := w.GetTimeConfig()
	if multiplier, ok := timeConfig["time_multiplier"].(float64); ok {
		return multiplier
	}
	return 1.0 // 默认倍率
}

// ShouldPauseWhenEmpty 检查是否在无人时暂停
func (w *World) ShouldPauseWhenEmpty() bool {
	timeConfig := w.GetTimeConfig()
	if pause, ok := timeConfig["pause_when_empty"].(bool); ok {
		return pause
	}
	return true // 默认暂停
}

// CanUserJoin 检查用户是否可以加入世界
func (w *World) CanUserJoin(userID string) bool {
	accessSettings := w.GetAccessSettings()

	// 检查是否公开
	if isPublic, ok := accessSettings["is_public"].(bool); ok && isPublic {
		// 检查人数限制
		if maxPlayers, ok := accessSettings["max_players"].(float64); ok {
			return w.CurrentPlayers < int(maxPlayers)
		}
		return w.CurrentPlayers < w.MaxPlayers
	}

	// 私有世界需要邀请
	return false
}

// GetGlobalVariables 获取所有全局变量
func (w *World) GetGlobalVariables() map[string]interface{} {
	if vars, ok := w.WorldState["global_variables"].(map[string]interface{}); ok {
		return vars
	}
	return make(map[string]interface{})
}
