package database

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// EnhancedCompatibility 增强的数据库兼容性管理器
// 基于现有Compatibility层进行增强，支持更复杂的JSON查询和AI友好的数据操作
type EnhancedCompatibility struct {
	*Compatibility // 继承现有功能
	db             *gorm.DB
	dbType         DatabaseType
}

// DatabaseType 数据库类型枚举
type DatabaseType string

const (
	PostgreSQL DatabaseType = "postgresql"
	SQLite     DatabaseType = "sqlite"
)

// NewEnhancedCompatibility 创建增强的兼容性管理器
func NewEnhancedCompatibility(db *gorm.DB) *EnhancedCompatibility {
	compatibility := NewCompatibility(db)
	
	// 检测数据库类型
	var dbType DatabaseType
	if compatibility.SupportsJSONB() {
		dbType = PostgreSQL
	} else {
		dbType = SQLite
	}
	
	return &EnhancedCompatibility{
		Compatibility: compatibility,
		db:            db,
		dbType:        dbType,
	}
}

// GetDatabaseType 获取数据库类型
func (ec *EnhancedCompatibility) GetDatabaseType() DatabaseType {
	return ec.dbType
}

// IsPostgreSQL 是否为PostgreSQL
func (ec *EnhancedCompatibility) IsPostgreSQL() bool {
	return ec.dbType == PostgreSQL
}

// IsSQLite 是否为SQLite
func (ec *EnhancedCompatibility) IsSQLite() bool {
	return ec.dbType == SQLite
}

// JSONQueryBuilder JSON查询构建器
type JSONQueryBuilder struct {
	compatibility *EnhancedCompatibility
	tableName     string
	columnName    string
}

// NewJSONQueryBuilder 创建JSON查询构建器
func (ec *EnhancedCompatibility) NewJSONQueryBuilder(tableName, columnName string) *JSONQueryBuilder {
	return &JSONQueryBuilder{
		compatibility: ec,
		tableName:     tableName,
		columnName:    columnName,
	}
}

// FieldEquals 字段等于查询
func (jqb *JSONQueryBuilder) FieldEquals(fieldPath string, value interface{}) (string, []interface{}) {
	if jqb.compatibility.IsPostgreSQL() {
		return fmt.Sprintf("%s->>'%s' = ?", jqb.columnName, fieldPath), []interface{}{value}
	} else {
		return fmt.Sprintf("JSON_EXTRACT(%s, '$.%s') = ?", jqb.columnName, fieldPath), []interface{}{value}
	}
}

// FieldLike 字段模糊查询
func (jqb *JSONQueryBuilder) FieldLike(fieldPath string, pattern string) (string, []interface{}) {
	if jqb.compatibility.IsPostgreSQL() {
		return fmt.Sprintf("%s->>'%s' LIKE ?", jqb.columnName, fieldPath), []interface{}{pattern}
	} else {
		return fmt.Sprintf("JSON_EXTRACT(%s, '$.%s') LIKE ?", jqb.columnName, fieldPath), []interface{}{pattern}
	}
}

// FieldExists 字段存在查询
func (jqb *JSONQueryBuilder) FieldExists(fieldPath string) (string, []interface{}) {
	if jqb.compatibility.IsPostgreSQL() {
		return fmt.Sprintf("%s ? '%s'", jqb.columnName, fieldPath), []interface{}{}
	} else {
		return fmt.Sprintf("JSON_EXTRACT(%s, '$.%s') IS NOT NULL", jqb.columnName, fieldPath), []interface{}{}
	}
}

// ContainsObject 包含对象查询
func (jqb *JSONQueryBuilder) ContainsObject(obj map[string]interface{}) (string, []interface{}) {
	if jqb.compatibility.IsPostgreSQL() {
		objJSON, _ := json.Marshal(obj)
		return fmt.Sprintf("%s @> ?", jqb.columnName), []interface{}{string(objJSON)}
	} else {
		// SQLite需要逐个字段检查
		var conditions []string
		var args []interface{}
		for key, value := range obj {
			conditions = append(conditions, fmt.Sprintf("JSON_EXTRACT(%s, '$.%s') = ?", jqb.columnName, key))
			args = append(args, value)
		}
		return strings.Join(conditions, " AND "), args
	}
}

// ArrayContains 数组包含查询
func (jqb *JSONQueryBuilder) ArrayContains(arrayPath string, value interface{}) (string, []interface{}) {
	if jqb.compatibility.IsPostgreSQL() {
		return fmt.Sprintf("%s->'%s' @> ?", jqb.columnName, arrayPath), []interface{}{fmt.Sprintf(`["%v"]`, value)}
	} else {
		return fmt.Sprintf(`EXISTS (
			SELECT 1 FROM json_each(%s, '$.%s') 
			WHERE value = ?
		)`, jqb.columnName, arrayPath), []interface{}{value}
	}
}

// NestedFieldEquals 嵌套字段等于查询
func (jqb *JSONQueryBuilder) NestedFieldEquals(path string, value interface{}) (string, []interface{}) {
	if jqb.compatibility.IsPostgreSQL() {
		// 处理嵌套路径，如 "theme.genre"
		pathParts := strings.Split(path, ".")
		var pgPath string
		if len(pathParts) > 1 {
			pgPath = fmt.Sprintf("%s", strings.Join(pathParts[:len(pathParts)-1], "->"))
			pgPath = fmt.Sprintf("%s->>'%s'", pgPath, pathParts[len(pathParts)-1])
		} else {
			pgPath = fmt.Sprintf("%s->>'%s'", jqb.columnName, path)
		}
		return fmt.Sprintf("%s = ?", pgPath), []interface{}{value}
	} else {
		return fmt.Sprintf("JSON_EXTRACT(%s, '$.%s') = ?", jqb.columnName, path), []interface{}{value}
	}
}

// IndexManager 索引管理器
type IndexManager struct {
	compatibility *EnhancedCompatibility
}

// NewIndexManager 创建索引管理器
func (ec *EnhancedCompatibility) NewIndexManager() *IndexManager {
	return &IndexManager{compatibility: ec}
}

// CreateJSONIndex 创建JSON索引
func (im *IndexManager) CreateJSONIndex(tableName, columnName string, paths []string) error {
	if im.compatibility.IsPostgreSQL() {
		// 创建GIN索引
		ginIndexName := fmt.Sprintf("idx_%s_%s_gin", tableName, columnName)
		ginQuery := fmt.Sprintf("CREATE INDEX IF NOT EXISTS %s ON %s USING GIN (%s)", ginIndexName, tableName, columnName)
		if err := im.compatibility.db.Exec(ginQuery).Error; err != nil {
			return fmt.Errorf("创建GIN索引失败: %w", err)
		}
		
		// 为常用路径创建表达式索引
		for _, path := range paths {
			exprIndexName := fmt.Sprintf("idx_%s_%s_%s", tableName, columnName, strings.ReplaceAll(path, ".", "_"))
			exprQuery := fmt.Sprintf("CREATE INDEX IF NOT EXISTS %s ON %s ((%s->>'%s'))", 
				exprIndexName, tableName, columnName, path)
			if err := im.compatibility.db.Exec(exprQuery).Error; err != nil {
				return fmt.Errorf("创建表达式索引失败 %s: %w", path, err)
			}
		}
	} else {
		// SQLite使用表达式索引
		for _, path := range paths {
			indexName := fmt.Sprintf("idx_%s_%s_%s", tableName, columnName, strings.ReplaceAll(path, ".", "_"))
			query := fmt.Sprintf("CREATE INDEX IF NOT EXISTS %s ON %s (JSON_EXTRACT(%s, '$.%s'))", 
				indexName, tableName, columnName, path)
			if err := im.compatibility.db.Exec(query).Error; err != nil {
				return fmt.Errorf("创建JSON表达式索引失败 %s: %w", path, err)
			}
		}
	}
	return nil
}

// CreateCompositeIndex 创建复合索引
func (im *IndexManager) CreateCompositeIndex(tableName string, columns []string, indexName string) error {
	if indexName == "" {
		indexName = fmt.Sprintf("idx_%s_%s", tableName, strings.Join(columns, "_"))
	}
	
	query := fmt.Sprintf("CREATE INDEX IF NOT EXISTS %s ON %s (%s)", 
		indexName, tableName, strings.Join(columns, ", "))
	
	if err := im.compatibility.db.Exec(query).Error; err != nil {
		return fmt.Errorf("创建复合索引失败: %w", err)
	}
	return nil
}

// TransactionManager 事务管理器
type TransactionManager struct {
	compatibility *EnhancedCompatibility
}

// NewTransactionManager 创建事务管理器
func (ec *EnhancedCompatibility) NewTransactionManager() *TransactionManager {
	return &TransactionManager{compatibility: ec}
}

// WithTransaction 在事务中执行操作
func (tm *TransactionManager) WithTransaction(ctx context.Context, fn func(*gorm.DB) error) error {
	return tm.compatibility.db.WithContext(ctx).Transaction(fn)
}

// BatchUpdate 批量更新JSON字段
func (tm *TransactionManager) BatchUpdateJSONField(ctx context.Context, tableName, columnName, whereClause string, updates map[string]interface{}, args ...interface{}) error {
	return tm.WithTransaction(ctx, func(tx *gorm.DB) error {
		if tm.compatibility.IsPostgreSQL() {
			// PostgreSQL使用jsonb_set函数
			for field, value := range updates {
				valueJSON, _ := json.Marshal(value)
				query := fmt.Sprintf("UPDATE %s SET %s = jsonb_set(%s, '{%s}', ?) WHERE %s", 
					tableName, columnName, columnName, field, whereClause)
				if err := tx.Exec(query, append([]interface{}{string(valueJSON)}, args...)...).Error; err != nil {
					return fmt.Errorf("批量更新字段 %s 失败: %w", field, err)
				}
			}
		} else {
			// SQLite需要读取-修改-写入
			// 这里简化处理，实际应用中可能需要更复杂的逻辑
			return fmt.Errorf("SQLite批量JSON更新需要在应用层实现")
		}
		return nil
	})
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	compatibility *EnhancedCompatibility
}

// NewPerformanceMonitor 创建性能监控器
func (ec *EnhancedCompatibility) NewPerformanceMonitor() *PerformanceMonitor {
	return &PerformanceMonitor{compatibility: ec}
}

// QueryStats 查询统计信息
type QueryStats struct {
	Duration    time.Duration `json:"duration"`
	RowsAffected int64        `json:"rows_affected"`
	DatabaseType string       `json:"database_type"`
	QueryType   string        `json:"query_type"`
}

// MeasureQuery 测量查询性能
func (pm *PerformanceMonitor) MeasureQuery(ctx context.Context, queryType string, fn func() *gorm.DB) (*QueryStats, error) {
	start := time.Now()
	
	result := fn()
	if result.Error != nil {
		return nil, result.Error
	}
	
	duration := time.Since(start)
	
	return &QueryStats{
		Duration:     duration,
		RowsAffected: result.RowsAffected,
		DatabaseType: string(pm.compatibility.dbType),
		QueryType:    queryType,
	}, nil
}

// ValidationHelper 验证助手
type ValidationHelper struct {
	compatibility *EnhancedCompatibility
}

// NewValidationHelper 创建验证助手
func (ec *EnhancedCompatibility) NewValidationHelper() *ValidationHelper {
	return &ValidationHelper{compatibility: ec}
}

// ValidateJSONStructure 验证JSON结构
func (vh *ValidationHelper) ValidateJSONStructure(data interface{}, requiredFields []string) error {
	jsonData, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("数据不是有效的JSON对象")
	}
	
	for _, field := range requiredFields {
		if _, exists := jsonData[field]; !exists {
			return fmt.Errorf("缺少必需字段: %s", field)
		}
	}
	
	return nil
}

// SanitizeJSONData 清理JSON数据
func (vh *ValidationHelper) SanitizeJSONData(data map[string]interface{}) map[string]interface{} {
	sanitized := make(map[string]interface{})
	
	for key, value := range data {
		// 移除空值和无效值
		if value != nil && value != "" {
			sanitized[key] = value
		}
	}
	
	return sanitized
}
